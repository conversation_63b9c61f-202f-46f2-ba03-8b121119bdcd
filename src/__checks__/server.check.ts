import { Api<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Frequency } from "checkly/constructs";

// Define the Checkly API Check for monitoring the /server-status route
const serverStatusCheck = new ApiCheck("server-status-check", {
  name: "Server Status Health Check",
  locations: ["ap-south-1"],
  frequency: Frequency.EVERY_2M,
  request: {
    method: "GET",
    url: "http://localhost:5000/api/v1/statistics/server-status",
    assertions: [AssertionBuilder.statusCode().equals(200)],
  },
});
