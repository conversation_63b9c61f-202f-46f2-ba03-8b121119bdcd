import express from "express";
import { UserRoutes } from "../modules/users/user.route";
import { ProductsRoutes } from "../modules/products/products.route";
import { PaymentRoutes } from "../modules/payments/payment.route";
import { ContentManagementRoutes } from "../modules/cms/cms.route";
import { OrderRoutes } from "../modules/orders/order.route";
import { CouponsRoutes } from "../modules/coupons/coupons.route";
import { StatisticsRoutes } from "../modules/statistics/statistics.route";
import { CategoryRoutes } from "../modules/categories/categories.route";
import { TransactionRoutes } from "../modules/transactions/transactions.route";
import { CoinPackageRoutes } from "../modules/coin_packages/coinPackages.route";
import { SubcategoryRoutes } from "../modules/subcategories/subcategories.route";

const router = express.Router();

const moduleRoutes = [
  {
    path: "/user",
    route: UserRoutes,
  },
  {
    path: "/cms",
    route: ContentManagementRoutes,
  },
  {
    path: "/product",
    route: ProductsRoutes,
  },
  {
    path: "/payment",
    route: PaymentRoutes,
  },
  {
    path: "/order",
    route: OrderRoutes,
  },
  {
    path: "/coupon",
    route: CouponsRoutes,
  },
  {
    path: "/statistic",
    route: StatisticsRoutes,
  },
  {
    path: "/category",
    route: CategoryRoutes,
  },
  {
    path: "/subcategory",
    route: SubcategoryRoutes,
  },
  {
    path: "/transaction",
    route: TransactionRoutes,
  },
  {
    path: "/coin_package",
    route: CoinPackageRoutes,
  },
];

moduleRoutes.forEach((route) => router.use(route.path, route.route));

export default router;
