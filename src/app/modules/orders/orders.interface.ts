import { Document, Model } from "mongoose";
import { IProducts } from "../products/products.interface";
import { IUser } from "../users/user.interface";
import { ITransaction } from "../transactions/transactions.interface";
import { TCoupons } from "../coupons/coupons.interface";

export type IOrder = {
  user: IUser["_id"];
  order_by: IUser["role"];
  payment_info: ITransaction["_id"] | null;
  products?: [
    {
      product: IProducts["_id"];
      total_quantity: number;
      selected_variant: string;
      price: number;
      discount: number;
      discounted_price: number;
      total_price: number;
      coins?: number;
      increase_delivery_charge_per_quantity?: number;
      default_delivery_charge?: number;
      total_delivery_charge?: number;
      is_free_delivery?: boolean;
    },
  ];
  status: string;
  transfer_to_courier: boolean;
  order_date: Date;
  order_serial_id: number;
  invoice_number: string;
  consignment_id?: number;
  tracking_code?: string;
  confirmed_date?: Date;
  courier_note?: string;
  courier_cod_amount?: number;
  estimated_delivery_time?: string;
  customer_name: string;
  customer_phone: string;
  customer_secondary_phone?: string;
  delivery_address?: {
    division: string;
    district: string;
    thana: string;
    address: string;
  };
  order_note?: string;
  applied_coupon_discount?: number;
  coupon?: TCoupons["_id"];
  applied_coins_discount?: number;
  coins_used?: number;
  advance_payment_amount?: number; // applicable when delivery charge is free or big amount
  sub_total: number;
  delivery_charge: number;
  total_amount: number;
  total_discount: number;
  COD: number;
  payment_type: "CASH_ON_DELIVERY" | "FULL_PAYMENT";
} & Document;

export type OrderModel = Model<IOrder, Record<string, unknown>>;
