import { Schema, model } from "mongoose";
import { IOrder, OrderModel } from "./orders.interface";

const orderSchema = new Schema<IOrder>(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    order_by: {
      type: String,
      required: true,
    },
    payment_info: {
      type: Schema.Types.ObjectId,
      ref: "Transactions",
      default: null,
    },
    products: [
      {
        product: {
          type: Schema.Types.ObjectId,
          ref: "Products",
        },
        total_quantity: {
          type: Number,
          required: true,
        },
        selected_variant: {
          type: String,
          required: true,
        },
        price: {
          type: Number,
          required: true,
        },
        discount: {
          type: Number,
          required: true,
        },
        discounted_price: {
          type: Number,
          required: true,
        },
        total_price: {
          type: Number,
          required: true,
        },
        coins: {
          type: Number,
          default: 0,
        },
        increase_delivery_charge_per_quantity: {
          type: Number,
          required: false,
        },
        default_delivery_charge: {
          type: Number,
          required: false,
        },
        total_delivery_charge: {
          type: Number,
          required: false,
        },
        is_free_delivery: {
          type: Boolean,
          default: false,
        },
      },
    ],
    status: {
      type: String,
      enum: [
        "ORDERED",
        "CONFIRMATED",
        "TRANSFER_TO_COURIER",
        "CANCELLED",
        "IN_TRANSIT",
        "DELIVERED",
        "RETURNED",
      ],
      default: "ORDERED",
    },
    transfer_to_courier: {
      type: Boolean,
      default: false,
    },
    order_date: {
      type: Date,
      default: () => new Date(),
    },
    order_serial_id: {
      type: Number,
      required: true,
    },
    invoice_number: {
      type: String,
      required: true,
    },
    consignment_id: {
      type: Number,
    },
    tracking_code: {
      type: String,
    },
    confirmed_date: {
      type: Date,
    },
    courier_note: {
      type: String,
    },
    courier_cod_amount: {
      type: Number,
    },
    estimated_delivery_time: {
      type: String,
    },
    customer_name: {
      type: String,
      required: true,
    },
    customer_phone: {
      type: String,
      required: true,
    },
    customer_secondary_phone: {
      type: String,
    },
    delivery_address: {
      division: {
        type: String,
      },
      district: {
        type: String,
      },
      thana: {
        type: String,
      },
      address: {
        type: String,
      },
    },
    order_note: {
      type: String,
    },
    applied_coupon_discount: {
      type: Number,
    },
    coupon: {
      type: Schema.Types.ObjectId,
      ref: "Coupons",
    },
    applied_coins_discount: {
      type: Number,
    },
    coins_used: {
      type: Number,
    },
    advance_payment_amount: {
      type: Number,
    },
    sub_total: {
      type: Number,
      required: true,
    },
    delivery_charge: {
      type: Number,
      required: true,
    },
    total_amount: {
      type: Number,
      required: true,
    },
    total_discount: {
      type: Number,
      required: true,
    },
    COD: {
      type: Number,
      required: true,
    },
    payment_type: {
      type: String,
      enum: ["CASH_ON_DELIVERY", "FULL_PAYMENT"],
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const Orders = model<IOrder, OrderModel>("Orders", orderSchema);

export default Orders;
