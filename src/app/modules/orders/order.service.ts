import { unset } from "node-global-storage";
import axios from "axios";
/* eslint-disable @typescript-eslint/no-explicit-any */

import ApiError from "../../middlewares/error";
import Orders from "./order.model";
import { PaymentUtils } from "../payments/payment.utils";
import { v4 as uuid } from "uuid";
import { IPaginationOptions } from "../../interfaces/pagination.interfaces";
import { paginationHelpers } from "../../helpers/paginationHelpers";
import Transactions from "../transactions/transactions.model";
import User from "../users/user.model";
import Products from "../products/products.model";
import Coupons from "../coupons/coupons.model";
import mongoose from "mongoose";
import { CourierMiddleware, TCourierPayload } from "./courier.middleware";

class Service {
  async executeOrder(paymentID: string) {
    const { data } = await axios.post(
      process.env.bkash_execute_payment_url as string,
      { paymentID },
      {
        headers: await PaymentUtils.bkash_headers(),
      }
    );
    return data;
  }

  async createOrder(payload: any, role: string, data?: any) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const isNoPaymentRequired = payload?.payable_amount === 0;
      let createTransaction: any = {};

      if (!isNoPaymentRequired) {
        const dateString = data?.paymentExecuteTime;
        const formattedDateString = dateString
          .replace(/:(\d{3})/, ".$1")
          .replace(" GMT+0600", "+06:00");
        const paymentDate = new Date(formattedDateString);

        const transactionData = {
          trx_id: data?.trxID,
          user_id: payload?.user_id,
          trx_status: data?.transactionStatus,
          payment_id: data?.paymentID,
          payment_date: paymentDate,
          amount: parseFloat(data?.amount),
          currency: data?.currency,
          payment_by: data?.customerMsisdn,
          event: "PRODUCT_PURCHASE",
          method: "BKASH",
        };

        const trxData = await Transactions.create([transactionData], { session });
        createTransaction = trxData[0];
      }

      const countOrder = await Orders.countDocuments().session(session);

      const couponInfo = {
        applied_coupon_discount: payload?.applied_coupon_discount,
        coupon: payload?.coupon,
      };

      const coinsInfo = {
        applied_coins_discount: payload?.applied_coins_discount,
        used_coins: payload?.used_coins,
      };

      const order = await Orders.create([{
        user: payload?.user_id,
        order_by: role,
        order_date: new Date(),
        payment_info: isNoPaymentRequired ? null : createTransaction?._id,
        products: payload?.products,
        order_serial_id: countOrder + 1,
        customer_name: payload?.customer_name,
        customer_phone: payload?.customer_phone,
        customer_secondary_phone: payload?.customer_secondary_phone || "",
        delivery_address: payload?.delivery_address,
        order_note: payload?.order_note,
        sub_total: payload?.sub_total,
        delivery_charge: payload?.delivery_charge,
        total_amount: payload?.total_amount,
        payment_amount: parseFloat(data?.payment_amount),
        advance_payment_amount: parseFloat(payload?.advance_payment_amount) || 0,
        payment_type: payload?.payment_type,
        estimated_delivery_time: payload?.estimated_delivery_time,
        invoice_number: "Inv" + uuid().replace(/-/g, "").substring(0, 5),
        status: "ORDERED",
        transfer_to_courier: false,
        COD: payload?.total_amount - (parseFloat(data?.amount) || 0),
        total_discount: (payload?.total_discount || 0),
        ...(payload?.coupon && couponInfo),
        ...(payload?.used_coins && coinsInfo),
        coins_used: payload?.coins_used || 0,
      }], { session });

      const createdOrder = order[0];

      const updateFields: any = {
        $inc: { total_orders: 1, pending_orders: 1 },
        $set: { last_order_date: new Date() },
      };

      if (payload.claimed_coins_id) {
        updateFields.$push = {
          used_coins_history: {
            coins: payload.used_coins,
            discount: payload.applied_coins_discount,
            reference: {
              name: "ORDER",
              id: (createdOrder._id as mongoose.Types.ObjectId).toString(),
            },
          },
        };
        updateFields.$pull = {
          claimed_coins: {
            reference: {
              name: "ORDER",
              id: payload.claimed_coins_id,
            },
          },
        };
      }

      await User.findByIdAndUpdate(
        payload?.user_id,
        updateFields,
        { session, new: true }
      );

      // Update product quantities and total_sold
      for (const productPayload of payload.products) {
        const product = await Products.findById(productPayload.product).session(session);
        if (product) {
          // const singleVariant = product.variant.find(
          //   (c) => c.name === productPayload.selected_variant
          // );
          // if (singleVariant) {
          //   singleVariant.available_quantity -= productPayload.total_quantity;
          // }
          product.total_sold += productPayload.total_quantity;
          await product.save({ session });
        }
      }

      // update coupon
      if (payload.coupon) {
        await Coupons.findByIdAndUpdate(
          payload.coupon,
          { $inc: { total_used: 1 } },
          { session, new: true }
        );
      }

      if (data?.amount > 0) {
        unset(payload?.user_id);
      }

      await session.commitTransaction();
      session.endSession();
      return createdOrder;
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }


  async createFailedTransaction(
    user_id: string,
    paymentID: string,
    status: string,
    amount: string,
    message: string
  ) {
    const transactionData = {
      trx_id: "Not found",
      user_id: user_id,
      trx_status: status,
      payment_id: paymentID,
      payment_date: new Date(Date.now()),
      amount: parseFloat(amount),
      currency: "BDT",
      payment_by: "Not found",
      message: message,
      event: "PRODUCT_PURCHASE",
      method: "BKASH",
    };
    const createTransaction = await Transactions.create(transactionData);
    return createTransaction;
  }

  async getAllOrders(options: IPaginationOptions, filter: string, searchQuery: string, role: string) {
    const { limit, page, skip } = paginationHelpers.calculatePagination(options);

    if (role !== "ADMIN") {
      throw new ApiError(400, "You are not authorized!")
    }

    const andConditions: any[] = [];

    // Search by order_serial_no or tracking_code
    if (searchQuery) {
      const numberQuery = Number(searchQuery);

      const searchConditions: any[] = [];
      if (!isNaN(numberQuery) && searchQuery?.length !== 11) {
        searchConditions.push({ order_serial_id: numberQuery });
      }
      else {
        searchConditions.push(
          { tracking_code: { $regex: searchQuery, $options: "i" } },
          { customer_phone: { $regex: searchQuery, $options: "i" } }
        );
      }

      andConditions.push({ $or: searchConditions });
    }


    // Apply filter logic
    switch (filter) {
      case "ALL":
        break;
      default:
        if (filter) {
          andConditions.push({ status: filter });
        }
    }

    const finalFilterCondition = andConditions.length > 0 ? { $and: andConditions } : {};

    const result = await Orders.find(finalFilterCondition)
      .populate([
        {
          path: "products",
          populate: {
            path: "product",
            select: "name thumbnail variant",
          },
        },
        {
          path: "user",
        },
        {
          path: "payment_info",
          match: { $ne: null },
        },
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);


    const total = await Orders.countDocuments(finalFilterCondition);

    return {
      meta: {
        page,
        limit,
        total,
      },
      data: result,
    };
  }

  async transferToCourier(
    role: string,
    order_id: string,
    payload: { note?: string; type?: "SINGLE" | "MULTIPLE" } = {}
  ) {
    if (role !== "ADMIN") {
      throw new ApiError(403, "You are not allowed to transfer an order");
    }

    const order = await Orders.findOne({ _id: order_id }).populate<{ user: { role: string } }>("user");

    if (!order) {
      throw new ApiError(404, "Invalid order id");
    }

    const courierPayload: TCourierPayload = {
      invoice: order?.invoice_number,
      recipient_name: order?.customer_name as string,
      recipient_phone: order?.customer_phone as string,
      recipient_address: `${order?.delivery_address?.address}, Upazila: ${order?.delivery_address?.thana}, District: ${order?.delivery_address?.district}`,
      cod_amount: order?.total_amount - order?.COD || 0,
      ...(payload.note && { note: payload.note }),
    };

    try {
      const courierRes: any = await CourierMiddleware.transfer_single_order(
        courierPayload,
        order?.user?.role as "USER" | "SELLER"
      );

      if (courierRes?.status === 200) {
        const result = await Orders.findByIdAndUpdate(
          order_id,
          {
            status: "TRANSFER_TO_COURIER",
            transfer_to_courier: true,
            $set: {
              consignment_id: courierRes?.consignment?.consignment_id,
              tracking_code: courierRes?.consignment?.tracking_code,
              confirmed_date: courierRes?.consignment?.created_at,
              courier_note: courierRes?.consignment?.note,
              cod_amount: courierRes?.consignment?.cod_amount,
            },
          },
          { new: true }
        );

        return result;
      } else {
        throw new ApiError(500, "Failed to transfer order to courier");
      }
    } catch (err: any) {
      console.error(err);
      throw new ApiError(
        500,
        "Error while transferring order to courier: " + err.message
      );
    }
  }

  async statusByTrackingCode(order_id: string) {
    const order = await Orders.findById(order_id).populate<{ user: { role: string } }>("user");
    if (!order) {
      throw new ApiError(404, "Invalid order id");
    }

    // Ensure certain statuses can't proceed to courier tracking
    if (
      order.status === "ORDERED" ||
      order.status === "CANCELLED" ||
      order.status === "RETURNED" ||
      order.status === "DELIVERED" ||
      !order.transfer_to_courier
    ) {
      throw new ApiError(
        400,
        `Order cannot be transferred to the courier due to its current status: ${order.status}`
      );
    }

    try {
      const trackingRes: any = await CourierMiddleware.status_by_tracking_code(
        order?.tracking_code as string,
        order?.user?.role as "USER" | "SELLER"
      );

      if (trackingRes?.status === 200) {
        let customStatus = "IN_TRANSIT";

        switch (trackingRes?.delivery_status) {
          case "hold":
          case "in_review":
            customStatus = "TRANSFER_TO_COURIER";
            break;
          case "pending":
          case "delivered_approval_pending":
          case "partial_delivered_approval_pending":
            customStatus = "IN_TRANSIT";
            break;
          case "delivered":
            customStatus = "DELIVERED";
            break;
          case "partial_delivered":
            customStatus = "IN_TRANSIT";
            break;
          case "cancelled":
          case "cancelled_approval_pending":
            customStatus = "RETURNED";
            break;
          case "unknown":
          case "unknown_approval_pending":
            customStatus = "CANCELLED";
            break;
          default:
            throw new ApiError(500, "Unknown delivery status received");
        }

        const session = await mongoose.startSession();
        session.startTransaction();

        try {
          const currentOrder = await Orders.findById(order_id).session(session);
          if (!currentOrder) {
            throw new ApiError(404, "Order not found");
          }

          const result = await Orders.findByIdAndUpdate(
            order_id,
            { status: customStatus },
            { new: true, session }
          );

          if (customStatus === "DELIVERED") {
            const totalCoins = currentOrder?.products?.reduce(
              (total, product) => total + (product.coins || 0),
              0
            );

            const user = await User.findByIdAndUpdate(
              { _id: currentOrder.user },
              {
                $inc: {
                  available_coins: totalCoins,
                  completed_orders: 1,
                  pending_orders: -1,
                },
              },
              { new: true, session }
            );

            if (!user) {
              throw new ApiError(404, "User not found");
            }
          }

          if (customStatus === "CANCELLED" || customStatus === "RETURNED") {
            const user = await User.findByIdAndUpdate(
              { _id: currentOrder.user },
              { $inc: { cancelled_orders: 1, pending_orders: -1 } },
              { session }
            );

            if (currentOrder.products && currentOrder.products.length > 0) {
              for (const pData of currentOrder.products) {
                const product = await Products.findById(pData.product).session(
                  session
                );
                if (product) {
                  // const variant = product.variant.find(
                  //   (c) => c.name === pData.selected_variant
                  // );
                  // if (variant) {
                  //   variant.available_quantity += pData.total_quantity;
                  // }
                  product.total_sold -= pData.total_quantity;
                  await product.save({ session });
                }
              }
            }
          }

          await session.commitTransaction();

          // Return response
          return {
            courier_status: trackingRes?.delivery_status,
            order_status: customStatus,
          };
        } catch (error) {
          await session.abortTransaction();
          throw error;
        } finally {
          session.endSession();
        }
      } else {
        throw new ApiError(500, "Failed to fetch status by tracking code");
      }
    } catch (err: any) {
      console.error(err);
      throw new ApiError(
        500,
        "Error while fetching status by tracking code: " + err.message
      );
    }
  }

  async updateOrderStatus(order_id: string, payload: any, isAdmin: string) {
    if (isAdmin !== "ADMIN") {
      throw new ApiError(403, "You are not allowed to update a status");
    }

    const orderStatus = [
      "ORDERED",
      "CONFIRMED",
      "TRANSFER_TO_COURIER",
      "CANCELLED",
      "IN_TRANSIT",
      "DELIVERED",
      "RETURNED",
    ];

    if (!orderStatus.includes(payload.status)) {
      throw new ApiError(400, "Invalid status");
    }

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const currentOrder = await Orders.findById(order_id).session(session);
      if (!currentOrder) {
        throw new ApiError(404, "Order not found");
      }
      const result = await Orders.findByIdAndUpdate(
        order_id,
        { status: payload?.status },
        { new: true, session }
      );

      if (payload.status === "DELIVERED") {
        const totalCoins = currentOrder?.products?.reduce(
          (total, product) => total + (product.coins || 0),
          0
        );

        const user = await User.findByIdAndUpdate(
          { _id: currentOrder.user },
          {
            $inc: {
              available_coins: totalCoins,
              completed_orders: 1,
              pending_orders: -1,
            },
          },
          { new: true, session }
        );

        if (!user) {
          throw new ApiError(404, "User not found");
        }
      }

      if (payload.status === "CANCELLED" || payload.status === "RETURNED") {
        const user = await User.findByIdAndUpdate(
          { _id: currentOrder.user },
          { $inc: { cancelled_orders: 1, pending_orders: -1 } },
          { session }
        );

        if (currentOrder.products && currentOrder.products.length > 0) {
          for (const pData of currentOrder.products) {
            const product = await Products.findById(pData.product).session(
              session
            );
            if (product) {
              // const variant = product.variant.find((c) => c.name === pData.selected_variant);
              // if (variant) {
              //   variant.available_quantity += pData.total_quantity;
              // }
              product.total_sold -= pData.total_quantity;
              await product.save({ session });
            }
          }
        }
      }

      await session.commitTransaction();
      return result;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  async getOrdersByUserId(user_id: string, status: string) {
    const statusCondition: { [key: string]: any } = {};
    if (status === "all") {
      statusCondition.status = {
        $in: [
          "ORDERED",
          "CONFIRMED",
          "TRANSFER_TO_COURIER",
          "IN_TRANSIT",
          "DELIVERED",
          "CANCELLED",
          "RETURNED",
        ],
      };
    } else if (status === "placed") {
      statusCondition.status = { $in: ["ORDERED"] };
    } else if (status === "to-ship") {
      statusCondition.status = { $in: ["TRANSFER_TO_COURIER", "IN_TRANSIT", "CONFIRMED"] };
    } else if (status === "to-received") {
      statusCondition.status = { $in: ["DELIVERED"] };
    } else if (status === "cancelled") {
      statusCondition.status = { $in: ["CANCELLED"] };
    } else if (status === "returned") {
      statusCondition.status = { $in: ["RETURNED"] };
    }

    const result = await Orders.find({
      user: user_id,
      ...statusCondition,
    })
      .populate([
        {
          path: "products",
          populate: {
            path: "product",
            select: "name thumbnail",
          },
        },
        {
          path: "payment_info",
          match: { $ne: null },
        },
      ])
      .sort({ updatedAt: -1 });
    return result;
  }

  async getOrderStatusBySerialId(orderSerialId: string) {
    const order = await Orders.findOne({
      order_serial_id: Number(orderSerialId),
    }).select("status");

    if (!order) {
      throw new ApiError(404, "Order not found with the given information");
    }

    return { status: order.status };
  }

  async cancelMultipleOrders(orderIds: string[], role: string) {
    if (role !== "ADMIN") {
      throw new ApiError(403, "You are not allowed to cancel orders");
    }

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      for (const orderId of orderIds) {
        const currentOrder = await Orders.findById(orderId).session(session);
        if (!currentOrder) {
          throw new ApiError(404, `Order with ID ${orderId} not found`);
        }

        const updatedOrder = await Orders.findByIdAndUpdate(
          orderId,
          { status: "CANCELLED" },
          { new: true, session }
        );

        const user = await User.findByIdAndUpdate(
          { _id: currentOrder.user },
          { $inc: { cancelled_orders: 1, pending_orders: -1 } },
          { session }
        );

        // if (currentOrder.products && currentOrder.products.length > 0) {
        //   for (const pData of currentOrder.products) {
        //     const product = await Products.findById(pData.product).session(
        //       session
        //     );

        //     if (product) {
        //       const color = product.colors.find((c) => c.color === pData.color);
        //       if (color) {
        //         color.available_quantity += pData.quantity;
        //       }
        //       await product.save({ session });
        //     }
        //   }
        // }

        // if (user?.role === "SELLER") {
        //   const charge_for_every_order_canceled = 80;

        //   await Sellers.findOneAndUpdate(
        //     { user: currentOrder.user },
        //     {
        //       $inc: {
        //         available_for_withdraw: -charge_for_every_order_canceled,
        //         reduce_profit_by_cancel_order: charge_for_every_order_canceled,
        //       },
        //     },
        //     { session }
        //   );
        // }
      }

      await session.commitTransaction();
      return orderIds;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  async updateOrderDeliveryStatus(
    order_id: string,
    payload: any,
    isAdmin: string
  ) {
    if (isAdmin !== "ADMIN") {
      throw new ApiError(403, "You are not allowed to update a status");
    }
    const isOrderExist = await Orders.findById(order_id);
    if (!isOrderExist) {
      throw new ApiError(404, "Order not found");
    }

    const result = await Orders.findByIdAndUpdate(
      order_id,
      {
        estimated_delivery_time: payload?.estimated_delivery_time,
      },
      {
        new: true,
      }
    );
    return result;
  }
}
export const OrderService = new Service();
