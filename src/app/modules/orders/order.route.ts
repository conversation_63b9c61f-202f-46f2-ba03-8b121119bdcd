import express from "express";
import { BkashMiddleware } from "../payments/bkash.middleware";
import { OrderController } from "./order.controller";
import verifyToken from "../../middlewares/verifyToken";
import { PaymentController } from "../payments/payment.controller";

const router = express.Router();

router.post("/create", verifyToken, OrderController.createOrder);

router.get("/create-with-payment", BkashMiddleware.bkash_auth, PaymentController.createPayment, OrderController.createOrderWithPayment);

router.get("/admin/all", verifyToken, OrderController.getAllOrders);
// router.patch(
//   "/transfer-to-courier/:order_id",
//   verifyToken,
//   OrderController.transferToCourier
// );
// router.get(
//   "/get-status-by-courier/:order_id",
//   verifyToken,
//   OrderController.statusByTrackingCode
// );
router.patch(
  "/admin/update-status/:id",
  verifyToken,
  OrderController.updateOrderStatus
);
// router.patch(
//   "/cancel-multiple",
//   verifyToken,
//   OrderController.cancelMultipleOrders
// );
router.patch(
  "/admin/update-estimated-delivery-time/:id",
  verifyToken,
  OrderController.updateOrderDeliveryStatus
);

router.get("/user/:user_id", verifyToken, OrderController.getOrdersByUserId);
router.get("/:serial_id", OrderController.getOrderBySerialId);

export const OrderRoutes = router;
