/* eslint-disable @typescript-eslint/no-explicit-any */
import { get } from "node-global-storage";
import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { OrderService } from "./order.service";

import { paginationFields } from "../../constants/paginationFields";
import pickQueries from "../../../shared/pickQueries";
import ApiError from "../../middlewares/error";

class Controller extends BaseController {
  // createOrder = async (req: Request, res: Response) => {
  //   // console.log("Order Payload", req.body);
  //   console.log("Order Payload acceptedd");
  //   // const validateData = await validateOrderPayload(req.body);
  //   // if (!validateData) {
  //   //   throw new ApiError(500, "Validation failed");
  //   // }
  //   // console.log("validation pass");
  //   const data = await OrderService.createOrder(req.body, req.role, {});
  //   console.log("data res", data);
  //   // return res.redirect(
  //   //   `${process.env.PRODUCTION_CLIENT_URL}/order/success`
  //   // );
  //   this.sendResponse(res, {
  //     statusCode: 200,
  //     success: true,
  //     message: "Order created successfully",
  //     data: data,
  //   });
  // };
  createOrder = async (req: Request, res: Response) => {
    try {
      // await validateOrderPayload(req.body);
      const data = await OrderService.createOrder(req.body, req.role, {});
      // return res.redirect(
      //   `${process.env.PRODUCTION_CLIENT_URL}/order/success?order_id=${data?.order_serial_id}`
      // );
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Order created successfully",
        data: data,
      });
    } catch (error: any) {
      console.error(error);
      this.sendResponse(res, {
        statusCode: error?.statusCode || 500,
        success: false,
        message: error?.message || "Something went wrong",
        data: null,
      });
    }
  };

  createOrderWithPayment = async (req: Request, res: Response) => {
    const { paymentID, status } = req.query;
    const user_id = req.query.user_id as string;
    const orderInfo = get(user_id);
    try {
      if (status === "cancel" || status === "failure") {
        const user_id = req.query.user_id as string;
        const orderInfo = get(user_id);
        await OrderService.createFailedTransaction(
          user_id,
          paymentID as string,
          status,
          orderInfo?.payable_amount,
          "Cancel or failure"
        );
        console.log("Payment Cancel or Failure", status);

        return res.redirect(
          `${process.env.PRODUCTION_CLIENT_URL}/payment/failed?message=${status}`
        );
      }
      else if (orderInfo?.payable_amount === 0) {
        await OrderService.createOrder(orderInfo, req.role);
        return res.redirect(
          `${process.env.PRODUCTION_CLIENT_URL}/order/success`
        );
      }
      else if (status == "success") {
        const executeData: any = await OrderService.executeOrder(
          paymentID as string
        );
        if (executeData && executeData?.statusMessage === "Successful") {
          await OrderService.createOrder(orderInfo, req.role, executeData);
          return res.redirect(
            `${process.env.PRODUCTION_CLIENT_URL}/order/success`
          );
        } else {
          const user_id = req.query.user_id as string;
          const orderInfo = get(user_id);
          await OrderService.createFailedTransaction(
            user_id,
            paymentID as string,
            status,
            orderInfo?.payable_amount,
            executeData?.statusMessage
          );
          return res.redirect(
            `${process.env.PRODUCTION_CLIENT_URL}/payment/failed?message=${executeData?.statusMessage}`
          );
        }
      }
    } catch (error: any) {
      console.log("Error In Create Order Controller", error);
      return res.redirect(
        `${process.env.PRODUCTION_CLIENT_URL}/payment/failed?message=${error?.message}`
      );
    }
  };

  getAllOrders = this.catchAsync(async (req: Request, res: Response) => {
    const options = pickQueries(req.query, paginationFields);

    const data = await OrderService.getAllOrders(
      options,
      req.query.filter as string,
      req.query.searchQuery as string,
      req.role
    );

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Orders find successfully",
      data: data,
    });
  });

  updateOrderStatus = this.catchAsync(async (req: Request, res: Response) => {
    const data = await OrderService.updateOrderStatus(
      req.params.id,
      req.body,
      req.role
    );

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Order Status Updated successfully",
      data: data,
    });
  });

  cancelMultipleOrders = this.catchAsync(
    async (req: Request, res: Response) => {
      const orderIds = req.query.order_ids?.toString().split(",");
      if (!orderIds || orderIds.length === 0) {
        return this.sendResponse(res, {
          statusCode: 400,
          success: false,
          message: "Order IDs are required",
        });
      }

      const data = await OrderService.cancelMultipleOrders(orderIds, req.role);

      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Orders cancelled successfully",
        data: data,
      });
    }
  );

  transferToCourier = this.catchAsync(async (req: Request, res: Response) => {
    const data = await OrderService.transferToCourier(
      req.role,
      req.params.order_id,
      req.body
    );

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Order transferred to courier successfully",
      data: data,
    });
  });
  statusByTrackingCode = this.catchAsync(
    async (req: Request, res: Response) => {
      const data = await OrderService.statusByTrackingCode(
        req.params?.order_id
      );

      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Order status found successfully",
        data: data,
      });
    }
  );
  updateOrderDeliveryStatus = this.catchAsync(
    async (req: Request, res: Response) => {
      const data = await OrderService.updateOrderDeliveryStatus(
        req.params.id,
        req.body,
        req.role
      );

      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Delivery Estimated Time Updated successfully",
        data: data,
      });
    }
  );

  getOrdersByUserId = this.catchAsync(async (req: Request, res: Response) => {
    const data = await OrderService.getOrdersByUserId(
      req.params.user_id,
      req.query.status as string
    );

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Orders by User Id find successfully",
      data: data,
    });
  });

  getOrderBySerialId = this.catchAsync(async (req: Request, res: Response) => {
    const data = await OrderService.getOrderStatusBySerialId(
      req.params.serial_id as string,
    );

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Order status found successfully",
      data: data,
    });
  });
}

export const OrderController = new Controller();
