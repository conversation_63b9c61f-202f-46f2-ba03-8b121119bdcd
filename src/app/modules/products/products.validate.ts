import { z as Z<PERSON> } from "zod";

const createZodSchema = Zod.object({
  body: Zod.object({
    // Basic Info
    name: Zod.string({
      required_error: "Name is required",
      invalid_type_error: "Name must be a string",
    }),
    description: Zod.string({
      required_error: "Description is required",
      invalid_type_error: "Description must be a string",
    }),
    sku: Zod.string({
      required_error: "SKU is required",
      invalid_type_error: "SKU must be a string",
    }).optional(),

    // Images
    thumbnail: Zod.string().url({ message: "Thumbnail must be a valid URL" }).optional(),
    slider_images: Zod.array(
      Zod.string().url({ message: "Slide image must be a valid URL" })
    ).optional(),

    // Pricing & Units
    unit: Zod.string().optional(),
    unit_price: Zod.number().optional(),

    discount_amount: Zod.number().min(0).optional(),
    discount_type: Zod.enum(["FLAT", "PERCENTAGE"]).optional(),

    // Shipping
    shipping_cost: Zod.number().min(0).optional(),
    shipping_cost_per_unit: Zod.number().min(0).optional(),

    // Quantity
    min_order_qty: Zod.number().min(1).optional(),
    max_order_qty: Zod.number().min(1).optional(),
    current_stock_qty: Zod.number().min(0).optional(),
    total_sold: Zod.number().min(0).optional(),

    // Delivery & Incentives
    approximately_delivery_time: Zod.string({
      required_error: "Delivery time is required",
    }),
    is_free_delivery: Zod.boolean().optional(),
    coin_per_order: Zod.number().min(0).optional(),

    // Policies
    warranty: Zod.string().optional(),
    return_policy: Zod.string().optional(),

    // Ratings
    total_rating: Zod.number().min(0).optional(),

    // Tags
    search_tags: Zod.array(Zod.string()).optional(),
    offer_tags: Zod.array(Zod.string()).optional(),

    // Attributes & Variants
    attributes: Zod.array(Zod.string()).optional(),

    variants: Zod.array(
      Zod.object({
        attribute_values: Zod.record(
          Zod.string(),
          Zod.string()
        ), // Example: { Size: "M", Color: "Red" }
        price: Zod.number({
          required_error: "Variant price is required",
        }),
        sku: Zod.string({
          required_error: "Variant SKU is required",
        }),
        available_quantity: Zod.number({
          required_error: "Available quantity is required",
        }).min(0, { message: "Available quantity cannot be negative" }),
        queue_order_quantity: Zod.number().min(0).optional(),
        image: Zod.string().url().optional(),
      })
    ).optional(),

    // Social
    social_links: Zod.object({
      facebook: Zod.string().url().optional(),
      instagram: Zod.string().url().optional(),
      youtube: Zod.string().url().optional(),
      tiktok: Zod.string().url().optional(),
    }).optional(),

    // Relations
    category: Zod.string({
      required_error: "Category ID is required",
    }).regex(/^[0-9a-fA-F]{24}$/, "Category ID must be a valid ObjectId"),

    subcategory: Zod.string({
      required_error: "Sub-category ID is required",
    }).regex(/^[0-9a-fA-F]{24}$/, "Sub-category ID must be a valid ObjectId"),
  }),
});

// Review Schema remains the same
const addReviewZodSchema = Zod.object({
  body: Zod.object({
    rating: Zod.number().min(1).max(5),
    comment: Zod.string(),
    user: Zod.string().regex(/^[0-9a-fA-F]{24}$/, {
      message: "User ID must be a valid ObjectId",
    }),
    images: Zod.array(Zod.string().url()).optional(),
  }),
});

export const ProductsValidationSchema = {
  createZodSchema,
  addReviewZodSchema,
};
