import { Schema, model } from "mongoose";
import { IProducts, ProductsModel } from "./products.interface";

// Variant combination schema
const variantCombinationSchema = new Schema(
  {
    attribute_values: {
      type: Map,
      of: String, // example: { Size: "M", Color: "Red" }
      required: true,
    },
    regular_price: { type: Number, default: 0 },
    sale_price: { type: Number, default: 0 },
    sku: { type: String, required: true },
    available_quantity: { type: Number, required: true },
    image: { type: String },
  },
  { _id: false }
);

// Product schema
const productsSchema = new Schema<IProducts>(
  {
    // Basic Info
    name: { type: String, required: true },
    slug: { type: String, required: true, unique: true, index: true },
    sku: { type: String, required: true, unique: true, index: true },
    description: { type: String, required: true },
    added_by: { type: Schema.Types.ObjectId, ref: "User", required: true },

    // Images
    thumbnail: { type: String, required: true },
    slider_images: [{ type: String }],

    // Unit & Pricing
    unit: { type: String },
    regular_price: { type: Number, default: 0 },
    sale_price: { type: Number, default: 0 },

    // Stock & Order
    min_order_qty: { type: Number, default: 1 },
    max_order_qty: { type: Number, default: 100 },
    current_stock_qty: { type: Number, default: 0 },
    total_sold: { type: Number, default: 0 },

    // Delivery & Offers
    approximately_delivery_time: { type: String, required: true },
    is_free_delivery: { type: Boolean, default: false },
    coin_per_order: { type: Number, default: 0 },
    shipping_cost: { type: Number, default: 80 },
    shipping_cost_per_unit: { type: Number, default: 0 },

    // Policy
    warranty: { type: String, default: "0" },
    return_policy: { type: String, default: "" },

    // Ratings & Reviews
    avg_rating: { type: Number, default: 0 },
    total_reviews: { type: Number, default: 0 },

    // Tags
    search_tags: [{ type: String }],
    offer_tags: [{ type: String }],

    // Socials
    social_links: {
      facebook: { type: String },
      instagram: { type: String },
      youtube: { type: String },
      tiktok: { type: String },
    },

    // Attributes & Variants
    attributes: [{ type: String }], // e.g., ["Size", "Color"]
    variants: [variantCombinationSchema],

    // Relations
    category: {
      type: Schema.Types.ObjectId,
      ref: "Category",
      required: true,
    },
    subcategory: {
      type: Schema.Types.ObjectId,
      ref: "SubCategory",
      required: true,
    },

    // Visibility
    is_published: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

const Products = model<IProducts, ProductsModel>("Products", productsSchema);
export default Products;
