import validateRequest from "../../middlewares/validateRequest";
import { ProductsController } from "./products.controller";
import express from "express";
import { ProductsValidationSchema } from "./products.validate";
import verifyToken from "../../middlewares/verifyToken";
import { UserRole } from "../users/user.constant";

const router = express.Router();

// Public routes
router.get("/", ProductsController.getAllProducts);
router.get("/ids", ProductsController.getProductsByMultipleId);
router.get("/:product_id", ProductsController.getProductById);
router.get("/slug/:product_slug", ProductsController.getProductBySlug);

// Admin routes
router.post(
  "/",
  verifyToken([UserRole.ADMIN]),
  validateRequest(ProductsValidationSchema.createZodSchema),
  ProductsController.addProduct
);
router.get(
  "/admin/all",
  verifyToken([UserRole.ADMIN]),
  ProductsController.getAllProductsForAdmin
);
router.delete(
  "/:product_id",
  verifyToken([UserRole.ADMIN]),
  ProductsController.deleteAProduct
);
router.patch(
  "/:product_id",
  verifyToken([UserRole.ADMIN]),
  ProductsController.updateAProduct
);
router.patch(
  "/admin/publish/:product_id",
  verifyToken([UserRole.ADMIN]),
  ProductsController.handlePublish
);

// review routes for admin
// router.patch(
//   "/update-admin-review/:product_id",
//   verifyToken([UserRole.ADMIN]),
//   ProductsController.updateReviewByAdmin
// );


// // review routes for user
// router.patch(
//   "/update-my-review/:product_id",
//   verifyToken([UserRole.USER]),
//   ProductsController.updateMyReview 
// );

// // review routes for user
// router.post(
//   "/add-review/:product_id",
//   verifyToken([UserRole.ADMIN, UserRole.USER]),
//   validateRequest(ProductsValidationSchema.addReviewZodSchema),
//   ProductsController.addReview
// );
// router.get(
//   "/get-review/:product_id",
//   ProductsController.getReviewByProductId
// );
 
// router.delete(
//   "/delete-admin-review/:product_id",
//   verifyToken([UserRole.ADMIN]),
//   ProductsController.deleteAdminReviewImg
// );

export const ProductsRoutes = router;
