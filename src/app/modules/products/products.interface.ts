

// products.interface.ts
import { Model, Schema } from "mongoose";
import { IUser } from "../users/user.interface";


// Variant combination for selected attribute values (e.g., Size: M, Color: Red)
export interface IVariantCombination {
  attribute_values: {
    [key: string]: string; // e.g., { Size: "M", Color: "Red" }
  };
  regular_price: number;
  sale_price: number;
  sku: string;
  available_quantity: number;
  image?: string; // optional: image for this variant
}

// Main Product interface
export interface IProducts {
  // Basic Info
  name: string;
  slug: string;
  sku: string;
  description: string;
  added_by: IUser["_id"];

  // Images
  thumbnail: string;
  slider_images?: string[];

  // Pricing
  unit?: string;
  regular_price: number;
  sale_price: number;

  // Stock & Order Constraints
  min_order_qty?: number;
  max_order_qty?: number;
  current_stock_qty: number;
  total_sold: number;

  // Delivery & Offers
  approximately_delivery_time: string; // default is "4 to 5 days"
  is_free_delivery?: boolean;
  coin_per_order?: number;
  shipping_cost?: number;
  shipping_cost_per_unit?: number;

  // Policy
  warranty?: string;
  return_policy?: string;

  // Ratings & Reviews
  avg_rating?: number;
  total_reviews?: number;

  // Tags
  search_tags?: string[];
  offer_tags?: string[];

  // Social Links
  social_links?: {
    facebook?: string;
    instagram?: string;
    youtube?: string;
    tiktok?: string;
  };

  // Variant System
  attributes?: string[]; // e.g., ["Size", "Color", "Material"]
  variants?: IVariantCombination[]; // all possible combinations

  // Category Reference
  category: Schema.Types.ObjectId;
  subcategory: Schema.Types.ObjectId;

  // Visibility
  is_published?: boolean;

  _id?: Schema.Types.ObjectId;
}

export interface ProductsModel extends Model<IProducts> { }