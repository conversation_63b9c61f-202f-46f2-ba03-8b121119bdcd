/* eslint-disable @typescript-eslint/no-explicit-any */

import ApiError from "../../middlewares/error";
import { IProducts } from "./products.interface";
import Products from "./products.model";
import {
  IFilterRequest,
  IPaginationOptions,
} from "../../interfaces/pagination.interfaces";
import { paginationHelpers } from "../../helpers/paginationHelpers";
import mongoose, { Schema } from "mongoose";
import { assetSearchableFields } from "../../constants/paginationFields";
import slugify from "slugify";

class Service {
  async addProduct(role: string, product: IProducts) {
    const result = await Products.create(product);
    if (!result) {
      throw new ApiError(400, "Something went wrong when adding product");
    }
    return result;
  }

  async getAllProducts(options: IPaginationOptions, filters: IFilterRequest) {
    const { limit, page, skip, sortBy, sortOrder } =
      paginationHelpers.calculatePagination(options);

    const {
      searchQuery,
      stock,
      categoryId,
      tags,
      isPublished,
      maxPrice,
      minPrice,
    } = filters;

    const andConditions = [];

    // Add search query filter
    if (searchQuery) {
      andConditions.push({
        $or: assetSearchableFields.map((field: string) => {
          if (field === "search_tags") {
            return {
              [field]: {
                $elemMatch: { $regex: searchQuery, $options: "i" },
              },
            };
          }
          return {
            [field]: {
              $regex: searchQuery,
              $options: "i",
            },
          };
        }),
      });
    }

    // Filter by category ID
    if (categoryId && mongoose.isValidObjectId(categoryId)) {
      andConditions.push({
        category: new Schema.Types.ObjectId(categoryId),
      });
    }

    // Stock filter
    if (stock === "in") {
      andConditions.push({
        "attributes.variants.available_quantity": { $gt: 0 },
      });
    } else if (stock === "out") {
      andConditions.push({
        "attributes.variants.available_quantity": { $lte: 0 },
      });
    }

    // Price range filter
    if (minPrice !== undefined && !isNaN(minPrice)) {
      andConditions.push({
        price: { $gte: minPrice },
      });
    }
    if (maxPrice !== undefined && !isNaN(maxPrice)) {
      andConditions.push({
        price: { $lte: maxPrice },
      });
    }

    // Tags filter
    if (tags && Array.isArray(tags) && tags.length > 0) {
      andConditions.push({
        search_tags: { $in: tags },
      });
    }

    // Publication status filter
    if (isPublished !== undefined) {
      andConditions.push({
        is_published: isPublished,
      });
    }

    const whereConditions: any =
      andConditions.length > 0 ? { $and: andConditions } : {};

    // Get total count
    const total = await Products.countDocuments(whereConditions);

    // Handle randomization and pagination
    let result: any[] = [];

    if (total > 0) {
      result = await Products.find(whereConditions)
        .sort({ [sortBy]: sortOrder === "asc" ? 1 : -1 })
        .skip(skip)
        .limit(limit)
        .populate({
          path: "category",
          model: "categories",
        })
        .populate({
          path: "subcategory",
          model: "subcategories",
        });
    } else {
      result = [];
    }

    return {
      meta: {
        page,
        limit,
        total,
      },
      data: result,
    };
  }

  async getAllProductsForAdmin(
    options: IPaginationOptions,
    filters: IFilterRequest
  ) {
    const { limit, page, skip, sortBy, sortOrder } =
      paginationHelpers.calculatePagination(options);

    const {
      searchQuery,
      stock,
      categoryId,
      tags,
      isPublished,
      maxPrice,
      minPrice,
    } = filters;

    const andConditions = [];

    // Add search query filter
    if (searchQuery) {
      andConditions.push({
        $or: assetSearchableFields.map((field: string) => {
          if (field === "search_tags") {
            return {
              [field]: {
                $elemMatch: { $regex: searchQuery, $options: "i" },
              },
            };
          }
          return {
            [field]: {
              $regex: searchQuery,
              $options: "i",
            },
          };
        }),
      });
    }

    // Filter by category ID
    if (categoryId && mongoose.isValidObjectId(categoryId)) {
      andConditions.push({
        category: new Schema.Types.ObjectId(categoryId),
      });
    }

    // Stock filter
    if (stock === "in") {
      andConditions.push({
        "attributes.variants.available_quantity": { $gt: 0 },
      });
    } else if (stock === "out") {
      andConditions.push({
        "attributes.variants.available_quantity": { $lte: 0 },
      });
    }

    // Price range filter
    if (minPrice !== undefined && !isNaN(minPrice)) {
      andConditions.push({
        price: { $gte: minPrice },
      });
    }
    if (maxPrice !== undefined && !isNaN(maxPrice)) {
      andConditions.push({
        price: { $lte: maxPrice },
      });
    }

    // Tags filter
    if (tags && Array.isArray(tags) && tags.length > 0) {
      andConditions.push({
        search_tags: { $in: tags },
      });
    }

    // Publication status filter
    if (isPublished !== undefined) {
      andConditions.push({
        is_published: isPublished,
      });
    }

    const whereConditions: any =
      andConditions.length > 0 ? { $and: andConditions } : {};

    // Get total count
    const total = await Products.countDocuments(whereConditions);

    // Handle randomization and pagination
    let result: any[] = [];

    if (total > 0) {
      result = await Products.find(whereConditions)
        .sort({ [sortBy]: sortOrder === "asc" ? 1 : -1 })
        .skip(skip)
        .limit(limit)
        .populate({
          path: "category",
          model: "categories",
        })
        .populate({
          path: "subcategory",
          model: "subcategories",
        });
    } else {
      result = [];
    }

    return {
      meta: {
        page,
        limit,
        total,
      },
      data: result,
    };
  }

  async getProductById(id: string) {
    const result = await Products.findById(id).select({ reviews: 0 }).populate({
      path: "category",
      model: "categories",
    });

    if (!result) {
      throw new ApiError(400, "Product not exist");
    }
    return result;
  }

  async getProductBySlug(slug: string) {
    const result = await Products.findOne({ slug })
      .select({ reviews: 0 })
      .populate({
        path: "category",
        model: "categories",
      });

    if (!result) {
      throw new ApiError(400, "Product not exist");
    }
    return result;
  }

  async getProductsByMultipleId(idArray: string[]) {
    const result = await Products.find({ _id: { $in: idArray } })
      .select({ admin_reviews: 0, reviews: 0 })
      .populate({
        path: "category",
        model: "categories",
      });

    return result;
  }

  async deleteAProduct(id: string, role: string) {
    const isProductExist = await Products.findById(id);
    if (!isProductExist) {
      throw new ApiError(400, "Product not exist");
    }

    const result = await Products.findByIdAndDelete(id).select({ _id: 1 });
    return result;
  }

  async updateAProduct(id: string, data: Partial<IProducts>, role: string) {
    const isExisted = await Products.findById(id);
    if (!isExisted) {
      throw new ApiError(400, "Product does not exist");
    }

    // If product_name is updated, generate a new slug
    if (data.name) {
      data.slug = slugify(data.name, { lower: true, strict: true });
    }

    const result = await Products.findByIdAndUpdate(id, data, { new: true });

    return result;
  }

  async handlePublish(id: string, value: "true" | "false", role: string) {
    const publish = value === "true" ? true : false;
    const result = await Products.findByIdAndUpdate(
      id,
      { is_published: publish },
      { new: true }
    );

    return result;
  }

  async addReview(
    product_id: string,
    data: {
      rating: number;
      comment: string;
      user: string;
      images?: string[];
    },
    role: string
  ) {
    const isProductExist = await Products.findById(product_id);
    if (!isProductExist) {
      throw new ApiError(400, "Product not exist");
    }
    const result = await Products.findByIdAndUpdate(
      product_id,
      {
        $push: {
          reviews: data,
        },
        // $inc: {
        //   total_admin_reviews: data?.images?.length || 0,
        // },
      },
      {
        new: true,
        populate: {
          path: "reviews.user",
        },
      }
    );

    return result;
  }

  // async updateReviewByAdmin(
  //   product_id: string,
  //   review_id: string,
  //   data: {
  //     rating: number;
  //     comment: string;
  //     user_id: string;
  //     images?: string[];
  //   }
  // ) {
  //   const isProductExist = await Products.findById(product_id);
  //   // .populate({
  //   //   path:'reviews.user',
  //   // });
  //   if (!isProductExist) {
  //     throw new ApiError(400, "Product not exist");
  //   }

  //   const review = isProductExist.reviews?.find((review) => {
  //     return review?._id.toString() === review_id.toString();
  //   });
  //   console.log({review})
  //   if (!review) {
  //     throw new ApiError(400, "Review not found for this user");
  //   }

  //   const result = await Products.findByIdAndUpdate(
  //     product_id,
  //     {
  //       $set: {
  //         "reviews.$[elem].rating": data.rating,
  //         "reviews.$[elem].comment": data.comment,
  //         "reviews.$[elem].images": data.images,
  //       },
  //     },
  //     {
  //       new: true,
  //       arrayFilters: [{ "elem.user": data.user_id }],
  //     }
  //   );

  //   return result;
  // }
  // async updateMyReview(
  //   product_id: string,
  //   review_id: string,
  //   data: {
  //     rating?: number;
  //     comment?: string;
  //     images?: string[];
  //   }
  // ) {
  //   const isProductExist = await Products.findById(product_id);
  //   if (!isProductExist) {
  //     throw new ApiError(400, "Product not exist");
  //   }
  //   const review = isProductExist.reviews?.find(
  //     (review) => review._id.toString() === review_id
  //   );
    
  //   if (!review) {
  //     throw new ApiError(400, "Review not found for this user");
  //   }
  //   const updateData: any = {};
  //   if (data.rating !== undefined) {
  //     updateData["reviews.$[elem].rating"] = data.rating;
  //   }
  //   if (data.comment !== undefined) {
  //     updateData["reviews.$[elem].comment"] = data.comment;
  //   }
  //   if (data.images !== undefined) {
  //     updateData["reviews.$[elem].images"] = data.images;
  //   }

  //   const result = await Products.findByIdAndUpdate(
  //     product_id,
  //     {
  //       $set: updateData,
  //     },
  //     {
  //       new: true,
  //       arrayFilters: [{ "elem._id": review_id }],
  //     }
  //   );

  //   return result;
  // }

  // async getReviewByProductId(product_id: string) {
  //   const result = await Products.findById(product_id)
  //     .select({
  //       reviews: 1,
  //     })
  //     .populate({
  //       path: "reviews",
  //       populate: {
  //         path: "user",
  //         select: "-password",
  //       },
  //     });
  //   return result;
  // }

  // async deleteAReview(
  //   product_id: string, 
  //   review_id : string
  // ) {
  //   const isProductExist: any = await Products.findById(product_id);
  //   if (!isProductExist) {
  //     throw new ApiError(400, "Product not exist");
  //   }

  //   const restOfReviews = isProductExist.reviews.filter(
  //     (review: any) => review._id.toString() !== review_id.toString()
  //   );

  //   const result = await Products.findByIdAndUpdate(
  //     product_id,
  //     {
  //       $set: {
  //         reviews: restOfReviews,
  //       },
  //     },
  //     { new: true }
  //   );

  //   return result;
  // }
}

export const ProductsService = new Service();
