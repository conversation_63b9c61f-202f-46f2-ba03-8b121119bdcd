import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { ProductsService } from "./products.service";

import pickQueries from "../../../shared/pickQueries";
import {
  productSortFields,
  paginationFields,
} from "../../constants/paginationFields";
import slugify from "slugify";

class Controller extends BaseController {
  addProduct = this.catchAsync(async (req: Request, res: Response) => {
    const payload = req.body;

    // Generate slug from product_name
    if (payload.name) {
      payload.slug = slugify(payload.name, {
        lower: true,
        strict: true,
      });
    }

    const data = await ProductsService.addProduct(req.role, payload);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Product added successfully",
      data: data,
    });
  });
  getAllProducts = this.catchAsync(async (req: Request, res: Response) => {
    const options = pickQueries(req.query, paginationFields);
    const filters = pickQueries(req.query, productSortFields);

    const data = await ProductsService.getAllProducts(options, filters);
    return this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Products Found successfully",
      data: data,
    });
  });

  getProductsByMultipleId = this.catchAsync(
    async (req: Request, res: Response) => {
      const ids = req.query.ids ? (req.query.ids as string).split(",") : [];
      const data = await ProductsService.getProductsByMultipleId(ids);
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Products found by multiple id successfully",
        data: data,
      });
    }
  );
  getAllProductsForAdmin = this.catchAsync(
    async (req: Request, res: Response) => {
      const options = pickQueries(req.query, paginationFields);
      const filters = pickQueries(req.query, productSortFields);

      const data = await ProductsService.getAllProductsForAdmin(
        options,
        filters
      );
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Products found for admin successfully",
        data: data,
      });
    }
  );
  getProductById = this.catchAsync(async (req: Request, res: Response) => {
    const data = await ProductsService.getProductById(
      req.params.product_id as string
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Product by id found successfully",
      data: data,
    });
  });

  getProductBySlug = this.catchAsync(async (req: Request, res: Response) => {
    const data = await ProductsService.getProductBySlug(
      req.params.product_slug as string
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Product by slug found successfully",
      data: data,
    });
  });

  deleteAProduct = this.catchAsync(async (req: Request, res: Response) => {
    const data = await ProductsService.deleteAProduct(
      req.params.product_id as string,
      req.role
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Product deleted successfully",
      data: data,
    });
  });
  updateAProduct = this.catchAsync(async (req: Request, res: Response) => {
    const data = await ProductsService.updateAProduct(
      req.params.product_id as string,
      req.body,
      req.role
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Product updated successfully",
      data: data,
    });
  });
  handlePublish = this.catchAsync(async (req: Request, res: Response) => {
    const data = await ProductsService.handlePublish(
      req.params?.product_id,
      req.query.value as "true" | "false",
      req.role
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Product publish update successfully",
      data: data,
    });
  });
  addReview = this.catchAsync(async (req: Request, res: Response) => {
    const data = await ProductsService.addReview(
      req.params.product_id,
      req.body,
      req.role
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Review Added successfully",
      data: data,
    });
  });
  // updateReviewByAdmin = this.catchAsync(async (req: Request, res: Response) => {
  //   const data = await ProductsService.updateReviewByAdmin(
  //     req.params.product_id,
  //     req.body.review_id,
  //     req.body
  //   );
  //   this.sendResponse(res, {
  //     statusCode: 200,
  //     success: true,
  //     message: "Review Added successfully",
  //     data: data,
  //   });
  // });
  // updateMyReview = this.catchAsync(async (req: Request, res: Response) => {
  //   if (req.body.user_id !== req.id) {
  //     return this.sendResponse(res, {
  //       statusCode: 403,
  //       success: false,
  //       message: "You are not allowed to update this review",
  //     });
  //   }
  //   const data = await ProductsService.updateMyReview(
  //     req.params.product_id,
  //     req.body.review_id,
  //     req.body
  //   );
  //   this.sendResponse(res, {
  //     statusCode: 200,
  //     success: true,
  //     message: "Review Added successfully",
  //     data: data,
  //   });
  // });
  // getReviewByProductId = this.catchAsync(
  //   async (req: Request, res: Response) => {
  //     const data = await ProductsService.getReviewByProductId(
  //       req.params.product_id
  //     );
  //     this.sendResponse(res, {
  //       statusCode: 200,
  //       success: true,
  //       message: "Review find successfully",
  //       data: data,
  //     });
  //   }
  // );
  // deleteAdminReviewImg = this.catchAsync(
  //   async (req: Request, res: Response) => {
  //     const data = await ProductsService.deleteAReview(
  //       req.params.product_id,
  //       req.body.review_id,
  //     );
  //     this.sendResponse(res, {
  //       statusCode: 200,
  //       success: true,
  //       message: "Review image deleted successfully",
  //       data: data,
  //     });
  //   }
  // );
}

export const ProductsController = new Controller();
