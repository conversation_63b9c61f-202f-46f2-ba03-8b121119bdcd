import { Schema, model } from "mongoose";
import { IUser, UserModel } from "./user.interface";
import { roles, status } from "./user.constant";

// Address Schema
export const Address = new Schema(
  {
    division: { type: String, required: true },
    district: { type: String, required: true },
    thana: { type: String, required: true },
    local_address: { type: String, required: true },
  },
  { _id: false }
);

// Used Coins History Schema
const UsedCoinsHistory = new Schema(
  {
    coins: { type: Number, required: true },
    discount: { type: Number, required: true },
    reference: {
      name: {
        type: String,
        enum: ["ORDER"],
        default: "ORDER",
      },
      id: { type: String, required: true },
    },
  },
  { _id: false }
);

// Claimed Coins Schema
const ClaimedCoins = new Schema(
  {
    coins: { type: Number, required: true },
    discount: { type: Number, required: true },
    reference: {
      name: {
        type: String,
        enum: ["ORDER"],
        default: "ORDER",
      },
      id: { type: String, required: true },
    },
  },
  { _id: false }
);

// User Schema
const userSchema = new Schema<IUser>(
  {
    full_name: { type: String, required: true },
    phone_number: { type: String, required: true },
    email: { type: String },
    profile_url: { type: String },
    role: { type: String, enum: roles, default: "USER" },
    address: { type: Address },
    password: { type: String, required: true },
    status: { type: String, enum: status, default: "ACTIVE" },
    is_verified_number: { type: Boolean, default: false },
    date_of_birth: { type: String },
    gender: { type: String, enum: ["MALE", "FEMALE"], required: false },
    default_delivery_address: { type: Address },
    permanent_address: { type: Address },
    favorite_items: {
      type: [Schema.Types.ObjectId],
      ref: "Products",
      default: [],
    },
    available_coins: { type: Number, default: 0 },
    used_coins_history: { type: [UsedCoinsHistory], default: [] },
    claimed_coins: { type: [ClaimedCoins], default: [] },
    total_orders: { type: Number, default: 0 },
    pending_orders: { type: Number, default: 0 },
    completed_orders: { type: Number, default: 0 },
    returned_orders: { type: Number, default: 0 },
    cancelled_orders: { type: Number, default: 0 },
    last_order_date: { type: String },
    last_active: { type: String },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

const User = model<IUser, UserModel>("User", userSchema);
export default User;
