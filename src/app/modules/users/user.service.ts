/* eslint-disable @typescript-eslint/no-explicit-any */

import { IUser } from "./user.interface";
import User from "./user.model";
import ApiError from "../../middlewares/error";
import { Utils } from "./user.utils";
import JwtHelpers from "../../helpers/jwtHelpers";
import { Secret } from "jsonwebtoken";
import { IPaginationOptions } from "../../interfaces/pagination.interfaces";
import { paginationHelpers } from "../../helpers/paginationHelpers";
import { OTPService } from "../otp/otp.service";
import { BulkSMSMiddleware } from "../../middlewares/bulksms.middleware";

class Service {
  async registerUser(data: IUser) {
    if (data?.phone_number) {
      const isExist = await User.findOne({
        phone_number: data.phone_number,
      });
      if (isExist && isExist?.is_verified_number) {
        throw new ApiError(400, "Number is already exist");
      }
    }

    if (data?.role && data?.role !== "USER") {
      throw new ApiError(400, "You are not allowed to register others role.");
    }

    const hashedPassword = await Utils.hash(data?.password);
    data.password = hashedPassword;

    const newUser = await User.findOneAndUpdate({ phone_number: data?.phone_number }, data, {
      new: true,
      upsert: true,
    }).select({
      password: 0,
    });

    if (!newUser) {
      throw new ApiError(400, "User registration failed");
    }

    const sendOTP = await BulkSMSMiddleware.send_otp(newUser?.phone_number);

    if (!sendOTP?.success) {
      throw new ApiError(400, sendOTP?.message || "Failed to send OTP");
    }

    return {
      status: sendOTP?.message,
      data: newUser,
    };
  }

  async verifyNumberWithOTP(payload: { phone_number: string; otp: number }) {
    const isUserExist = await User.findOne({
      phone_number: payload.phone_number,
    });
    if (!isUserExist) {
      throw new ApiError(400, "User does not exist");
    }
    if (isUserExist?.is_verified_number) {
      throw new ApiError(400, "Phone number is already verified");
    }
    const res: { validation: boolean } = await OTPService.verifyOTP(
      payload.phone_number,
      payload.otp
    );
    if (!res.validation) {
      throw new ApiError(400, "Invalid OTP");
    }
    const user = await User.findOneAndUpdate(
      { phone_number: payload.phone_number },
      { is_verified_number: true, status: "ACTIVE" },
      { new: true }
    ).select({ password: 0 });
    if (!user) {
      throw new ApiError(400, "User not found");
    }
    const access_token = JwtHelpers.createToken(
      {
        id: user.id,
        phone_number: user.phone_number,
        role: user.role,
      },
      process.env.JWT_TOKEN as Secret,
      process.env.ACCESS_EXPIRES_IN as string
    );

    const refresh_token = JwtHelpers.createToken(
      {
        id: user?.id,
        phone_number: user?.phone_number,
      },
      process.env.JWT_REFRESH_TOKEN as Secret,
      process.env.REFRESH_EXPIRES_IN as string
    );
    return {
      data: user,
      access_token,
      refresh_token,
      message: "Phone number verified successfully",
    };
  }

  async loginUser(payload: { phone_number: string; password: string }) {
    const isUserExist: any = await User.findOne({
      phone_number: payload.phone_number,
    });

    if (!isUserExist) {
      throw new ApiError(400, "User does not exist");
    }

    if (isUserExist?.status === "INACTIVE") {
      throw new ApiError(400, "Your account has been deactivated.");
    }

    const hashedPassword = await Utils.hash(payload?.password);
    const passwordMatch = hashedPassword == isUserExist?.password;
    if (!passwordMatch) {
      throw new ApiError(400, "Incorrect password");
    }

    const result = await User.findOne({
      phone_number: payload.phone_number,
    }).select({
      password: 0,
    });

    const access_token = JwtHelpers.createToken(
      {
        id: result?.id,
        phone_number: result?.phone_number,
        role: result?.role,
      },
      process.env.JWT_TOKEN as Secret,
      process.env.ACCESS_EXPIRES_IN as string
    );

    const refresh_token = JwtHelpers.createToken(
      {
        id: result?.id,
        phone_number: result?.phone_number,
      },
      process.env.JWT_REFRESH_TOKEN as Secret,
      process.env.REFRESH_EXPIRES_IN as string
    );

    return {
      data: result,
      access_token,
      refresh_token,
    };
  }

  async ResetPassword(payload: { phone_number: string; new_password: string }) {
    // const getUser: any = await User.findOne({
    //   phone_number: payload.phone_number,
    // });

    // if (!getUser) {
    //   throw new ApiError(400, "Account not found");
    // }
    // if (getUser?.status === "INACTIVE") {
    //   throw new ApiError(400, "Your account has been deactivated");
    // }
    // const getOTPRecord = await OTPRecords.findOne({
    //   phone_number: payload.phone_number,
    // });
    // if (!getOTPRecord) {
    //   throw new ApiError(400, "You are unauthorized to reset password");
    // } else if (getOTPRecord?.event !== "RESET_PASSWORD") {
    //   throw new ApiError(400, "OTP already in used for another event");
    // } else if (!getOTPRecord?.is_validated) {
    //   throw new ApiError(400, "OTP unverified");
    // } else {
    //   const hashedPassword = await Utils.hash(payload?.new_password);
    //   await User.updateOne(
    //     { phone_number: payload.phone_number },
    //     { $set: { password: hashedPassword } }
    //   );
    //   return {
    //     reset_password: true,
    //   };
    // }
  }

  async getAllUser(options: IPaginationOptions, searchQuery: string) {
    const { limit, page, skip, sortBy, sortOrder } =
      paginationHelpers.calculatePagination(options);

    // Constructing the search condition
    const searchCondition: any = {};
    if (searchQuery) {
      searchCondition.$or = [
        { full_name: { $regex: searchQuery, $options: "i" } },
        { phone_number: { $regex: searchQuery, $options: "i" } },
      ];
    }

    const result = await User.find(searchCondition)
      .select({ password: 0 })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(searchCondition);

    return {
      meta: {
        page,
        limit,
        total,
      },
      data: result,
    };
  }

  async getUserByToken(_id: string) {
    const result = await User.findById(_id).select({ password: 0 });
    if (!result) {
      throw new ApiError(400, "User not found!");
    }
    return result;
  }

  async getUserById(id: string) {
    const result = await User.findById(id).select({ password: 0 });
    if (!result) {
      throw new ApiError(400, "User not exist");
    }
    return result;
  }

  async changePasswordByUserId(user_id: string, payload: any) {
    const isUserExist = await User.findById(user_id);

    if (!isUserExist) {
      throw new ApiError(404, "User not found");
    }

    const hashPassword = await Utils.hash(payload?.old_password);

    if (isUserExist?.password !== hashPassword) {
      throw new ApiError(409, "Password do not match");
    }

    const newPassword = await Utils.hash(payload?.new_password);

    const result = await User.findByIdAndUpdate(
      user_id,
      {
        password: newPassword,
      },
      {
        new: true,
      }
    );

    return result;
  }

  async updateUser(id: string, payload: any) {
    const isUserExist = await User.findById(id);
    if (!isUserExist) {
      throw new ApiError(400, "User not exist");
    }

    if (payload?.phone_number || payload?.password) {
      throw new ApiError(400, "Sensitive data cannot be updated");
    }

    let result;
    if (payload) {
      result = await User.findByIdAndUpdate(id, payload, {
        new: true,
      }).select({
        password: 0,
      });
    }
    return result;
  }

  async accountDeactivation(user_id: string) {
    const isUserExist = await User.findById(user_id);
    if (!isUserExist) {
      throw new ApiError(400, "User not exist");
    }
    const result = await User.findByIdAndUpdate(
      user_id,
      {
        status: "INACTIVE",
      },
      {
        new: true,
      }
    ).select({
      password: 0,
    });
    return result;
  }

  async accountActivation(user_id: string) {
    const isUserExist = await User.findById(user_id);
    if (!isUserExist) {
      throw new ApiError(400, "User not exist");
    }
    const result = await User.findByIdAndUpdate(
      user_id,
      {
        status: "ACTIVE",
      },
      {
        new: true,
      }
    ).select({
      password: 0,
    });
    return result;
  }

  async deleteUser(user_id: string, isAdmin: string) {
    
    const isUserExist = await User.findById(user_id);

    if (!isUserExist) {
      throw new ApiError(400, "User not exist");
    }

    const result = await User.findByIdAndDelete(user_id).select({ _id: 1 });

    return result;
  }
}

export const UserService = new Service();
