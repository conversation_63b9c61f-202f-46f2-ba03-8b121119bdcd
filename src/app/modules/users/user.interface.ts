import { Document, Model } from "mongoose";
import { IRoles, IStatus } from "./user.constant";

export type IAddress = {
  division: string;
  district: string;
  thana: string;
  local_address: string;
};

type IUsedCoins = {
  coins: number;
  discount: number;
  reference: {
    name: "ORDER";
    id: string;
  };
};

type IClaimedCoins = {
  coins: number;
  discount: number;
  reference: {
    name: "ORDER";
    id: string;
  };
};

export type IUser = {
  full_name: string;
  phone_number: string;
  email?: string;
  profile_url?: string;
  role: IRoles;
  address?: IAddress;
  password: string;
  status: IStatus;
  is_verified_number: boolean;
  date_of_birth?: string;
  gender?: string;
  default_delivery_address?: IAddress;
  permanent_address?: IAddress;
  favorite_items?: string[];
  available_coins?: number;
  used_coins_history?: IUsedCoins[];
  claimed_coins?: IClaimedCoins[];
  total_orders?: number;
  pending_orders?: number;
  completed_orders?: number;
  returned_orders?: number;
  cancelled_orders?: number;
  last_order_date?: string;
  last_active?: string;
} & Document;

export type UserModel = Model<IUser, Record<string, unknown>>;
