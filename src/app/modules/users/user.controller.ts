import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { UserService } from "./user.service";

import { paginationFields } from "../../constants/paginationFields";
import pickQueries from "../../../shared/pickQueries";
import ApiError from "../../middlewares/error";

type CustomRequest = Request & {
  id?: string;
  phone_number?: string;
  role?: string;
};

class Controller extends BaseController {
  registerUser = this.catchAsync(async (req: Request, res: Response) => {
    const data = await UserService.registerUser(req.body);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "User registered successfully",
      data: data,
    });
  });

  verifyNumberWithOTP = this.catchAsync(async (req: Request, res: Response) => {
    const data = await UserService.verifyNumberWithOTP(req.body);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Phone number verified successfully",
      data: data,
    });
  });

  loginUser = this.catchAsync(async (req: CustomRequest, res: Response) => {
    const data = await UserService.loginUser(req.body);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "User login successfully",
      data: data,
    });
  });

  resetPassword = this.catchAsync(async (req: CustomRequest, res: Response) => {
    const data = await UserService.ResetPassword(req.body);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Password reset successfully",
      data: data,
    });
  });

  getAllUser = this.catchAsync(async (req: Request, res: Response) => {
    const options = pickQueries(req.query, paginationFields);
    
    const data = await UserService.getAllUser(
      options,
      req.query.searchQuery as string
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Users find successfully",
      data: data,
    });
  });
  getUserByToken = this.catchAsync(
    async (req: CustomRequest, res: Response) => {
      const data = await UserService.getUserByToken(req.id);
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "User find successfully",
        data: data,
      });
    }
  );
  getUserById = this.catchAsync(async (req: CustomRequest, res: Response) => {
    const data = await UserService.getUserById(req.params.user_id);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "User find successfully",
      data: data,
    });
  });
  updateUser = this.catchAsync(async (req: CustomRequest, res: Response) => {
    const data = await UserService.updateUser(req.id, req.body);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "User updated successfully",
      data: data,
    });
  });

  accountDeactivation = this.catchAsync(async (req: Request, res: Response) => {
    const user_id = req.params.user_id as string;
    const data = await UserService.accountDeactivation(user_id);

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "User Deactivated successfully",
      data: data,
    });
  });

  accountActivation = this.catchAsync(async (req: Request, res: Response) => {
    const user_id = req.params.user_id as string;
    const data = await UserService.accountActivation(user_id);

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "User Activated successfully",
      data: data,
    });
  });

  deleteUser = this.catchAsync(async (req: CustomRequest, res: Response) => {
    const data = await UserService.deleteUser(req.params.user_id, req.role);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "User deleted successfully",
      data: data,
    });
  });
  changePasswordByUserId = this.catchAsync(
    async (req: CustomRequest, res: Response) => {
      const data = await UserService.changePasswordByUserId(
        req.params.user_id,
        req.body
      );
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "User password changed successfully",
        data: data,
      });
    }
  );
}

export const UserController = new Controller();
