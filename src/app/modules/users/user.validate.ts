import { z as Zod } from "zod";

const createZodSchema = Zod.object({
  body: Zod.object({
    full_name: Zod.string({
      required_error: "Full Name is required",
      invalid_type_error: "Full Name must be string",
    }),
    password: Zod.string({
      required_error: "Password is required",
      invalid_type_error: "Password must be string",
    }),
    phone_number: Zod.string({
      required_error: "Valid Phone Number is required",
      invalid_type_error: "Valid Phone Number must be string",
    }),
  }),
});

const verifyNumberZodSchema = Zod.object({
  body: Zod.object({
    phone_number: Zod.string({
      required_error: "Phone Number is required",
    })
      .min(10, "Phone Number must be at least 10 digits")
      .max(15, "Phone Number must be at most 15 digits"),
    otp: Zod.number({
      required_error: "OTP is required",
      invalid_type_error: "OTP must be a number",
    }),
  }),
});

export const UserValidationSchema = {
  createZodSchema,
  verifyNumberZodSchema,
};
