export type IRoles =
  | "USER" // Regular user customer
  | "VP" // VP as like vendor
  | "ADMIN" // Admin with high privileges of the second level access for managing the platform

export type IStatus = "ACTIVE" | "INACTIVE" | "DELETED" | "BANNED" | "PENDING";

export const roles: IRoles[] = [
  "USER",
  "ADMIN",
];
export const status: IStatus[] = [
  "ACTIVE",
  "INACTIVE",
  "DELETED",
  "BANNED",
  "PENDING",
];

export const UserRole = {
  USER: "USER" as IRoles,
  VP: "VP" as IRoles,
  ADMIN: "ADMIN" as <PERSON>R<PERSON><PERSON>,
};

export enum UserStatus {
  "ACTIVE",
  "INACTIVE",
  "DELETED",
  "BANNED",
  "PENDING",
}
