import express from "express";
import { User<PERSON>ontroller } from "./user.controller";
import validateRequest from "../../middlewares/validateRequest";
import { UserValidationSchema } from "./user.validate";
import verifyToken from "../../middlewares/verifyToken";
import { UserRole } from "./user.constant";
const router = express.Router();

// User routes
router.post(
  "/register",
  validateRequest(UserValidationSchema.createZodSchema),
  UserController.registerUser
);
router.patch(
  "/verify-otp",
  validateRequest(UserValidationSchema.verifyNumberZodSchema),
  UserController.verifyNumberWithOTP
);
router.post("/login", UserController.loginUser);
router.get("/", verifyToken(), UserController.getUserByToken);
// router.patch("/:user_id", verifyToken(), UserController.updateUser);
router.get("/:user_id", verifyToken(), UserController.getUserById);
// router.patch("/reset-password", verifyToken([UserRole.ADMIN]),]), UserController.resetPassword);
// router.post(
//   "/change-password/:user_id",
//   verifyToken([UserRole.ADMIN]),]),
//   UserController.changePasswordByUserId
// );

// Admin routes
router.get(
  "/all",
  verifyToken([UserRole.ADMIN]),
  UserController.getAllUser
);
router.patch(
  "/deactivate/:user_id",
  verifyToken([UserRole.ADMIN]),
  UserController.accountDeactivation
);
router.patch(
  "/activate/:user_id",
  verifyToken([UserRole.ADMIN]),
  UserController.accountActivation
);
router.delete(
  "/:user_id",
  verifyToken([UserRole.ADMIN]),
  UserController.deleteUser
);

export const UserRoutes = router;
