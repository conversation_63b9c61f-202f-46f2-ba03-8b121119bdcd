import { z as Zod } from "zod";

const EventEnumValues = [
  "RESET_PASSWORD",
  "CREATE_ACCOUNT",
  "MODERATOR_LOGIN",
] as const;
type EventEnumType = (typeof EventEnumValues)[number];

// Define the enum schema with a custom error message
const EventEnum = Zod.enum(EventEnumValues, {
  errorMap: (issue, _ctx) => {
    if (issue.code === "invalid_enum_value") {
      return {
        message: "Event must be either 'RESET_PASSWORD' or 'CREATE_ACCOUNT'",
      };
    }
    return { message: "Invalid value" };
  },
});

const createZodSchema = Zod.object({
  body: Zod.object({
    phone_number: Zod.string({
      required_error: "Phone number is required",
      invalid_type_error: "Phone number must be a string",
    }),
    event: EventEnum,
  }),
});

const verifyOTPSchema = Zod.object({
  body: Zod.object({
    phone_number: Zod.string({
      required_error: "Phone number is required",
      invalid_type_error: "Phone number must be a string",
    }),
    otp: Zod.string({
      required_error: "OTP is required",
      invalid_type_error: "OTP must be a number",
    }),
    event: EventEnum,
  }),
});

export const OtpValidationSchema = {
  createZodSchema,
  verifyOTPSchema,
};
