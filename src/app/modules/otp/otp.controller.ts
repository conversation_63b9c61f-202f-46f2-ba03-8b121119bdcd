import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { OTPService } from "./otp.service";

class Controller extends BaseController {
  // addOTPRecord = this.catchAsync(async (req: Request, res: Response) => {
  //   const body = req.body;
  //   const formatData = {
  //     phone_number: body.phone_number,
  //     otp: body.otpCode,
  //     event: body.event
  //   };
  //   const data = await OTPService.addOTPRecord(formatData);
  //   this.sendResponse(res, {
  //     statusCode: 200,
  //     success: true,
  //     message: "OTP send successfully",
  //   });
  // });
  // verifyOtp = this.catchAsync(async (req: Request, res: Response) => {
  //   const body = req.body;
  //   const data = await OTPService.verifyOTP(
  //     body.phone_number,
  //     body.otp,
  //     body.event || ""
  //   );
  //   this.sendResponse(res, {
  //     statusCode: 200,
  //     success: true,
  //     message: "O<PERSON> successfully verified",
  //     data: data,
  //   });
  // });
}

export const OTPController = new Controller();
