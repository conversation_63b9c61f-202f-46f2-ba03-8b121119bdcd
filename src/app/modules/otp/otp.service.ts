import ApiError from "../../middlewares/error";
import OTPRecords from "./otp.model";

class Service {
  async addOTPRecord(payload: { number: string; otp: number }) {
    const now = new Date();
    // Check if an unexpired OTP already exists
    const existingOtp = await OTPRecords.findOne({
      phone_number: payload.number,
      expired_at: { $gt: now },
      is_validated: false,
    });
    if (existingOtp) {
      return {
        has_otp: true,
      };
    }
    // Otherwise, generate and save a new OTP
    const expiredAt = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes
    let data: any = { ...payload };
    data.expired_at = expiredAt;
    const filter = { phone_number: data.number };
    const update = { ...data, is_validated: false };
    const options = { new: true, upsert: true };
    const result = await OTPRecords.findOneAndUpdate(filter, update, options);
    if (!result) {
      throw new Error("Something went wrong when adding OTP record");
    }
    return result;
  }
  async verifyOTP(phone_number: string, otp: number) {
    const otpRecord: any = await OTPRecords.findOne({
      phone_number,
      otp,
    });
    if (!otpRecord) {
      throw new ApiError(422, "Invalid OTP");
    }
    if (otpRecord.is_validated) {
      throw new ApiError(422, "OTP has already been used");
    }
    const expiredAt = new Date(otpRecord.expired_at);
    if (expiredAt < new Date()) {
      throw new ApiError(422, "OTP has expired");
    }
    otpRecord.is_validated = true;
    await otpRecord.save();
    return {
      validation: true,
    };
  }
}

export const OTPService = new Service();
