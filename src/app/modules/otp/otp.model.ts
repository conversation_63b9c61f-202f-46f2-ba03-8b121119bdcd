import { Schema, model } from "mongoose";
import { IOTP, OTPModel } from "./otp.interface";

const OtpSchema = new Schema<IOTP>(
  {
    phone_number: {
      type: String,
      required: true,
    },
    otp: {
      type: Number,
      required: true,
    },
    is_validated: {
      type: Boolean,
      required: true,
      default: false,
    },
    expired_at: {
      type: Date,
      default: null,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const OTPRecords = model<IOTP, OTPModel>("otp-record", OtpSchema);

export default OTPRecords;
