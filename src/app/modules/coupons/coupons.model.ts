import { Schema, model } from "mongoose";
import { CouponsModel, TCoupons } from "./coupons.interface";

const couponsSchema = new Schema<TCoupons>(
  {
    promo_code: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ["ACTIVE", "EXPIRED"], // Define the allowed values here
      default: "ACTIVE",
      required: false,
    },
    min_amount: {
      type: Number,
      required: true,
    },
    discount: {
      type: Number,
      required: true,
    },
    total_used: {
      type: Number,
      default: 0,
      required: false,
    },
    is_public: {
      type: Boolean,
      required: true,
      default: true,
    },
    expiredAt: {
      type: Date,
      default: null,
      required: false,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const Coupons = model<TCoupons, CouponsModel>("Coupons", couponsSchema);
export default Coupons;
