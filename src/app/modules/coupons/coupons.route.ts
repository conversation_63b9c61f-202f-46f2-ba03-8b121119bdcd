import express from "express";
import validateRequest from "../../middlewares/validateRequest";
import { CouponsValidationSchema } from "./coupons.validate";
import { CouponsController } from "./coupons.controller";
import verifyToken from "../../middlewares/verifyToken";

const router = express.Router();

// Public routes
router.get("/public/active", CouponsController.getPublicCoupons);
router.post("/validate", CouponsController.couponCodeValidate);

// Admin routes
router.post(
  "/",
  verifyToken,
  validateRequest(CouponsValidationSchema.createZodSchema),
  CouponsController.addCoupon
);
router.get(
  "/admin/active",
  verifyToken,
  CouponsController.getAllActiveCoupons
);
router.get("/admin/expired", verifyToken, CouponsController.getExpiredCoupons);
router.patch(
  "/:coupon_id",
  verifyToken,
  CouponsController.updateCouponStatus
);

export const CouponsRoutes = router;
