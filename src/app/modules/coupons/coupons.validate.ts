import { z as Zod } from "zod";

const createZodSchema = Zod.object({
  body: Zod.object({
    promo_code: Zod.string({
      required_error: "Promo code is required",
      invalid_type_error: "Promo code must be a string",
    }),
    discount: Zod.number({
      required_error: "Discount is required",
      invalid_type_error: "Discount must be a number",
    }),
    min_amount: Zod.number({
      required_error: "Min amount is required",
      invalid_type_error: "Min amount must be a number",
    }),
    is_public: Zod.boolean({
      required_error: "is_public is required",
      invalid_type_error: "is_public must be a boolean",
    }),
  }),
});

export const CouponsValidationSchema = {
  createZodSchema,
};
