import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { CouponsService } from "./coupons.service";

class Controller extends BaseController {
  addCoupon = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CouponsService.addCoupon(req.body, req.role);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Coupon added successfully",
      data: data,
    });
  });
  getPublicCoupons = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CouponsService.getPublicCoupons();
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Active coupons found successfully",
      data: data,
    });
  });
  getAllActiveCoupons = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CouponsService.getAllActiveCoupons(req.role);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Active coupons found successfully",
      data: data,
    });
  });
  getExpiredCoupons = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CouponsService.getExpiredCoupons(req.role);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Expired coupons found successfully",
      data: data,
    });
  });
  couponCodeValidate = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CouponsService.couponCodeValidate(req.body.promo_code);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Coupon code validation successfully",
      data: data,
    });
  });
  updateCouponStatus = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CouponsService.updateCouponStatus(
      req.role,
      req.params.coupon_id,
      req.body.status
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Coupon status updated successfully",
      data: data,
    });
  });
}

export const CouponsController = new Controller();
