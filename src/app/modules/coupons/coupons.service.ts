import ApiError from "../../middlewares/error";
import { TCoupons } from "./coupons.interface";
import Coupons from "./coupons.model";

class Service {
  async addCoupon(coupon: TCoupons, role: string) {
    if (role !== "ADMIN") {
      throw new ApiError(403, "You have no access to add coupon");
    }
    const isExistCode = await Coupons.findOne({
      promo_code: coupon.promo_code,
    });
    if (isExistCode) {
      throw new ApiError(400, "Promo code already exist");
    }
    const result = await Coupons.create(coupon);
    if (!result) {
      throw new ApiError(400, "Something went wrong to adding coupon");
    }
    return result;
  }

  async getPublicCoupons() {
    const result = await Coupons.find({
      status: { $in: "ACTIVE" },
      is_public: true,
    });
    if (!result) {
      throw new ApiError(400, "Coupon not found");
    }
    return result;
  }
  async getExpiredCoupons(role: string) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new ApiError(403, "You have no access to get expired coupons");
    }
    const result = await Coupons.find({
      status: { $in: "EXPIRED" },
    });
    if (!result) {
      throw new ApiError(400, "Coupon not found");
    }
    return result;
  }

  async getAllActiveCoupons(role: string) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new ApiError(403, "You have no access to get coupons");
    }
    const result = await Coupons.find({
      status: { $in: "ACTIVE" },
    });
    if (!result) {
      throw new ApiError(400, "Coupon not found");
    }
    return result;
  }

  async couponCodeValidate(code: string) {
    const result = await Coupons.findOne({
      promo_code: code,
      status: "ACTIVE",
    });
    if (result) {
      return {
        validate: true,
        coupon: result,
      };
    } else {
      return {
        validate: false,
        coupon: {},
      };
    }
  }

  async updateCouponStatus(role: string, couponId: string, status: string) {
    if (role !== "ADMIN") {
      throw new ApiError(403, "You have no access to update coupon");
    }
    const isExisted = await Coupons.findById(couponId);
    if (!isExisted) {
      throw new ApiError(400, "Coupon not exist");
    }
    const result = await Coupons.findByIdAndUpdate(
      couponId,
      { status: status, expiredAt: status === "EXPIRED" ? new Date() : null },
      { new: true }
    );
    return result;
  }
}

export const CouponsService = new Service();
