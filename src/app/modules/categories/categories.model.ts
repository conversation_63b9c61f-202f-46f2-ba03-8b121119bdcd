import { model, Schema } from "mongoose";
import { CategoryModel, ICategory } from "./categories.interface";

const categoriesSchema = new Schema<ICategory>(
  {
    name_en: {
      type: String,
      required: true,
    },
    name_bn: {
      type: String,
      required: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      index: true,
      lowercase: true,
      trim: true,
    },
    description_en: {
      type: String,
      required: true,
    },
    description_bn: {
      type: String,
      required: true,
    },
    image_url: {
      type: String,
      required: true,
    },
    position: {
      type: Number,
      required: true,
    },
    subcategories: {
      type: Schema.Types.ObjectId,
      ref: "Subcategories"
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const Categories = model<ICategory, CategoryModel>(
  "categories",
  categoriesSchema
);

export default Categories;
