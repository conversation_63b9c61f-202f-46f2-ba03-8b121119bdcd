import express from "express";
import validateRequest from "../../middlewares/validateRequest";
import { CategoryValidationSchema } from "./categories.validate";
import verifyToken from "../../middlewares/verifyToken";
import { CategoryController } from "./categories.controller";
import { UserRole } from "../users/user.constant";
import multer from "multer";
const router = express.Router();

const upload = multer();

router.post(
  "/",
  verifyToken([UserRole.ADMIN]),
  upload.single("image"), // Using the multer middleware to handle FormData
  CategoryController.createCategory
);
router.patch(
  "/:category_id",
  verifyToken([UserRole.ADMIN]),
  validateRequest(CategoryValidationSchema.updateCategoryZodSchema),
  CategoryController.updateCategory
);
router.get("/", CategoryController.getAllCategory);
router.get("/:id", CategoryController.getCategoryById);

export const CategoryRoutes = router;
