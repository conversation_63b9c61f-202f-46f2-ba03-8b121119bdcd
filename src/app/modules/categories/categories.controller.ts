import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { CategoryService } from "./categories.service";
import slugify from "slugify";
import { CategoryValidationSchema } from "./categories.validate";
import { UploadThingImageStorage } from "../../helpers/fileUploader";

class Controller extends BaseController {
  private uploadStorage = new UploadThingImageStorage();

  createCategory = this.catchAsync(async (req: Request, res: Response) => {
    // Parse and normalize fields from FormData
    const body: any = { ...req.body, image_url: "" };
    if (body.position) body.position = Number(body.position);
    if (body.subcategories && typeof body.subcategories === "string") {
      try {
        body.subcategories = JSON.parse(body.subcategories);
      } catch {
        body.subcategories = [body.subcategories];
      }
    }
    console.log(body);
    const parsed =
      await CategoryValidationSchema.createZodSchema.shape.body.safeParseAsync(
        body
      );

    if (!parsed.success) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: parsed.error.errors,
      });
    }

    let imageUrl = "";
    if (req.file) {
      console.log(req.file.mimetype);
      try {
        const uploadRes = await this.uploadStorage.uploadImage(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );
        imageUrl = uploadRes.url;
        console.log(uploadRes);
      } catch (error) {
        return res.status(500).json({
          success: false,
          message: "Image upload failed",
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Prepare payload
    const payload = {
      ...body,
      image_url: imageUrl,
    };

    // Generate slug from name_en
    if (payload?.name_en) {
      payload.slug = slugify(payload?.name_en, {
        lower: true,
        strict: true,
      });
    }
    const data = await CategoryService.createCategory(payload);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Category created successfully",
      data: data,
    });
  });
  updateCategory = this.catchAsync(async (req: Request, res: Response) => {
    // Parse and normalize fields from FormData
    const body: any = { ...req.body };
    if (body.position) body.position = Number(body.position);
    if (body.subcategories && typeof body.subcategories === "string") {
      try {
        body.subcategories = JSON.parse(body.subcategories);
      } catch {
        body.subcategories = [body.subcategories];
      }
    }

    let imageUrl = "";
    if (req.file) {
      try {
        const uploadRes = await this.uploadStorage.uploadImage(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );
        imageUrl = uploadRes.url;
        body.image_url = imageUrl;
      } catch (error) {
        return res.status(500).json({
          success: false,
          message: "Image upload failed",
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Update slug from name_en
    if (body?.name_en) {
      body.slug = slugify(body?.name_en, {
        lower: true,
        strict: true,
      });
    }

    const data = await CategoryService.updateCategory(
      body,
      req.params.category_id
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Category updated successfully",
      data: data,
    });
  });
  getAllCategory = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CategoryService.getAllCategory();
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Categories found successfully",
      data: data,
    });
  });
  getCategoryById = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CategoryService.getCategoryById(req.params.id);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Category found successfully",
      data: data,
    });
  });
}

export const CategoryController = new Controller();
