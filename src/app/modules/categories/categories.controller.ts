import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { CategoryService } from "./categories.service";
import slugify from "slugify";
import { CategoryValidationSchema } from "./categories.validate";

class Controller extends BaseController {
  createCategory = this.catchAsync(async (req: Request, res: Response) => {
    // Parse and normalize fields from FormData
    const body: any = { ...req.body };
    if (body.position) body.position = Number(body.position);
    if (body.subcategories && typeof body.subcategories === "string") {
      try {
        body.subcategories = JSON.parse(body.subcategories);
      } catch {
        body.subcategories = [body.subcategories];
      }
    }
    // Validate non-image fields from req.body
    const parsed =
      await CategoryValidationSchema.createZodSchema.body.safeParseAsync(body);

    if (!parsed.success) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: parsed.error.errors,
      });
    }

    let imageUrl = "";
    if (req.file) {
      const uploadRes = await uploadthing.upload({
        files: [
          {
            buffer: req.file.buffer,
            name: req.file.originalname,
            type: req.file.mimetype,
          },
        ],
        endpoint: "imageUploader", // You may need to configure this in uploadthing dashboard
      });
      imageUrl = uploadRes[0]?.url;
    }

    // Prepare payload
    const payload = {
      ...body,
      image_url: imageUrl,
    };

    // Generate slug from name_en
    if (payload?.name_en) {
      payload.slug = slugify(payload?.name_en, {
        lower: true,
        strict: true,
      });
    }
    const data = await CategoryService.createCategory(payload);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Category created successfully",
      data: data,
    });
  });
  updateCategory = this.catchAsync(async (req: Request, res: Response) => {
    const payload = req.body;
    // Update slug from name_en
    if (payload?.name_en) {
      payload.slug = slugify(payload?.name_en, {
        lower: true,
        strict: true,
      });
    }
    const data = await CategoryService.updateCategory(
      payload,
      req.params.category_id
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Category updated successfully",
      data: data,
    });
  });
  getAllCategory = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CategoryService.getAllCategory();
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Categories found successfully",
      data: data,
    });
  });
  getCategoryById = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CategoryService.getCategoryById(req.params.id);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Category found successfully",
      data: data,
    });
  });
}

export const CategoryController = new Controller();
