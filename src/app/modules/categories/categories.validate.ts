import { z as Zod } from "zod";

const createZodSchema = Zod.object({
  body: Zod.object({
    name_en: Zod.string({
      required_error: "Name (English) is required",
      invalid_type_error: "Name (English) must be a string",
    }),
    name_bn: Zod.string({
      required_error: "Name (Bangla) is required",
      invalid_type_error: "Name (Bangla) must be a string",
    }),
    description_en: Zod.string({
      required_error: "Description (English) is required",
      invalid_type_error: "Description (English) must be a string",
    }),
    description_bn: Zod.string({
      required_error: "Description (Bangla) is required",
      invalid_type_error: "Description (Bangla) must be a string",
    }),
    image_url: Zod.string({
      required_error: "Image URL is required",
      invalid_type_error: "Image URL must be a string",
    }),
    position: Zod.number({
      required_error: "Position is required",
      invalid_type_error: "Position must be a number",
    }),
    subcategories: Zod.array(Zod.string()).optional(),
  }),
});

const updateCategoryZodSchema = Zod.object({
  body: Zod.object({
    name_en: Zod.string().optional(),
    name_bn: Zod.string().optional(),
    description_en: Zod.string().optional(),
    description_bn: Zod.string().optional(),
    slug: Zod.string().optional(),
    image_url: Zod.string().optional(),
    position: Zod.number().optional(),
    subcategories: Zod.array(Zod.string()).optional(),
  }),
});

export const CategoryValidationSchema = {
  createZodSchema,
  updateCategoryZodSchema,
};
