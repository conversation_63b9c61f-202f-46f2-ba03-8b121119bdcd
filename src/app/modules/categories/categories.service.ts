import ApiError from "../../middlewares/error";
import { ICategory } from "./categories.interface";
import Categories from "./categories.model";
import slugify from "slugify";

class Service {
  async createCategory(category: ICategory) {
    const isNameExist = await Categories.findOne({
      $or: [{ name_en: category.name_en }, { name_bn: category.name_bn }],
    });
    if (isNameExist) {
      throw new ApiError(500, "Category name already exist");
    }
    const totalCategory = await Categories.countDocuments({});
    const isPositionExist = await Categories.findOne({
      position: category.position,
    });
    if (isPositionExist) {
      isPositionExist.position = totalCategory + 1;
      await isPositionExist.save();
    }
    // Generate slug from category name
    category.slug = slugify(category.name_en, {
      lower: true,
      strict: true,
    });

    const result = await Categories.create(category);
    if (!result) {
      throw new ApiError(500, "Failed to create category");
    }
    return result;
  }
  async updateCategory(
    data: Partial<ICategory>, // accept partial updates
    id: string
  ) {
    const category = await Categories.findById(id);
    if (!category) {
      throw new ApiError(400, "Category not exist");
    }
    // Generate slug from category name
    if (data.name_en && data.name_en) {
      data.slug = slugify(data.name_en, {
        lower: true,
        strict: true,
      });
    }
    const updatedFields: any = { ...data }; // dynamic fields

    // if position is being updated
    if (data.position !== undefined && data.position !== category.position) {
      // Swap the position
      await Categories.findOneAndUpdate(
        { position: data.position, _id: { $ne: id } },
        { $set: { position: category.position } }
      );
    }

    const updatedCategory = await Categories.findByIdAndUpdate(
      id,
      { $set: updatedFields },
      { new: true }
    );

    return updatedCategory;
  }

  async getAllCategory() {
    const categories = await Categories.find({}).sort({ position: 1 });
    return categories;
  }

  async getCategoryById(id: string) {
    const category = await Categories.findById(id);
    if (!category) {
      throw new ApiError(404, "Category not found!")
    }
    return category;
  }
}

export const CategoryService = new Service();
