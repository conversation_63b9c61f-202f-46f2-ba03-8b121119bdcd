// cms.model.ts
import { Schema, model } from "mongoose";
import { ContentManagementModel, IContentManagement } from "./cms.interface";

const contentManagerSchema = new Schema<IContentManagement>(
  {
    banners: [
      {
        products_id: [
          {
            type: String,
            required: false,
          },
        ],
        banner_url: {
          type: String,
          required: false,
        },
      },
    ],
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const ContentManagement = model<IContentManagement, ContentManagementModel>(
  "CMS",
  contentManagerSchema
);

export default ContentManagement;
