import express from "express";
import verifyToken from "../../middlewares/verifyToken";
import { ContentManagementController } from "./cms.controller";

const router = express.Router();

// Public routes
router.get("/banners", ContentManagementController.getBanners);

// Admin routes
router.patch("/add/banner", verifyToken, ContentManagementController.addBanner);
router.patch(
  "/add/banner/:bannerId/:productId",
  verifyToken,
  ContentManagementController.addProductIdToBanner
);
router.patch(
  "/delete/banner/:bannerId/:productId",
  verifyToken,
  ContentManagementController.deleteProductIdToBanner
);
router.delete(
  "/delete-banner/:bannerId",
  verifyToken,
  ContentManagementController.deleteBanner
);

export const ContentManagementRoutes = router;
