import ContentManagement from "./cms.model";
import ApiError from "../../middlewares/error";

class Service {
  async addBanner(data: { products_id: string[]; banner_url: string }) {
    // Find the existing document or create a new one if none exists
    const existingDocument = await ContentManagement.findOne();
    if (!existingDocument) {
      const newDocument = await ContentManagement.create({
        banners: [
          { products_id: data.products_id, banner_url: data.banner_url },
        ],
      });
      return newDocument;
    }

    // Update the existing document by pushing a new banner object to the `banner` array
    const result = await ContentManagement.findOneAndUpdate(
      { _id: existingDocument._id },
      {
        $push: {
          banners: {
            products_id: data.products_id,
            banner_url: data.banner_url,
          },
        },
      },
      { new: true }
    );

    if (!result) {
      throw new ApiError(400, "Banner update failed");
    }

    return result;
  }

  async addProductIdToBanner(bannerId: string, productId: string) {
    const result = await ContentManagement.findOneAndUpdate(
      { "banners._id": bannerId },
      {
        $addToSet: { "banners.$.products_id": productId },
      },
      { new: true }
    );

    if (!result) {
      throw new ApiError(400, "Banner not found");
    }

    return result;
  }

  async deleteProductIdToBanner(bannerId: string, productId: string) {
    const result = await ContentManagement.findOneAndUpdate(
      { "banners._id": bannerId },
      {
        $pull: { "banners.$.products_id": productId },
      },
      { new: true }
    );

    if (!result) {
      throw new ApiError(400, "Banner not found");
    }

    return result;
  }

  async getBanners() {
    const result = await ContentManagement.findOne().select({ banners: 1 });
    return result;
  }

  async deleteBanner(bannerId: string) {
    const existingDocument = await ContentManagement.findOne();
    if (!existingDocument) {
      throw new ApiError(404, "No document found to update");
    }

    const result = await ContentManagement.findOneAndUpdate(
      { "banners._id": bannerId },
      {
        $pull: { banners: { _id: bannerId } },
      },
      { new: true }
    );

    if (!result) {
      throw new ApiError(400, "Failed to delete banner");
    }

    return result;
  }
}

export const ContentManagementService = new Service();
