import { Document, Model } from "mongoose";
import { IUser } from "../users/user.interface";

export type ITransaction = {
  trx_id: string;
  user_id: IUser["id"];
  trx_status: string;
  payment_id: string;
  payment_date: Date;
  amount: number;
  currency: string;
  payment_by: string;
  message?: string;
  event: string;
  method: "BKASH" | "NAGAD";
} & Document;

export type TransactionModel = Model<ITransaction, Record<string, unknown>>;
