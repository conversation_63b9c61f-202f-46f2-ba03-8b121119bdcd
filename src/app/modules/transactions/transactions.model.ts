import { model, Schema } from "mongoose";
import { ITransaction, TransactionModel } from "./transactions.interface";

const transactionSchema = new Schema<ITransaction>(
  {
    trx_id: { type: String, required: true },
    user_id: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    trx_status: { type: String, required: true },
    payment_id: { type: String, required: true },
    payment_date: { type: Date, required: true },
    amount: { type: Number, required: true },
    currency: { type: String, default: "BDT" },
    payment_by: { type: String, required: true },
    message: { type: String, required: false },
    event: {
      type: String,
      enum: ["SELLER_REQUEST", "PRODUCT_PURCHASE"],
      required: true,
    },
    method: { type: String, enum: ["BKASH", "NAGAD"], default: "BKASH" },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const Transactions = model<ITransaction, TransactionModel>(
  "Transactions",
  transactionSchema
);

export default Transactions;
