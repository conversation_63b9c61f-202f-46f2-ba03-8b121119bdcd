import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { TransactionService } from "./transactions.service";

class Controller extends BaseController {
  createTransaction = this.catchAsync(async (req: Request, res: Response) => {
    const data = await TransactionService.createTransaction(req.body);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Transaction created successfully",
      data: data,
    });
  });
}

export const TransactionController = new Controller();
