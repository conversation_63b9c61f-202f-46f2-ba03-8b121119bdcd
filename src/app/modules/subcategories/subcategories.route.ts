import express from "express";
import validateRequest from "../../middlewares/validateRequest";
import { SubcategoryValidationSchema } from "./subcategories.validate";
import verifyToken from "../../middlewares/verifyToken";
import { SubcategoryController } from "./subcategories.controller";
import { UserRole } from "../users/user.constant";
const router = express.Router();

router.post(
  "/",
  verifyToken([UserRole.ADMIN]),
  validateRequest(SubcategoryValidationSchema.createZodSchema),
  SubcategoryController.createsSubcategory
);

router.patch(
  "/:sub_category_id",
  verifyToken([UserRole.ADMIN]),
  validateRequest(SubcategoryValidationSchema.updateSubcategoryZodSchema),
  SubcategoryController.updateSubcategory
);
router.get("/", SubcategoryController.getAllSubcategory);
router.get("/:id", SubcategoryController.getSubcategoryById);

router.get("/by-category/:category_id", SubcategoryController.getSubcategoriesByCategoryId);

export const SubcategoryRoutes = router;
