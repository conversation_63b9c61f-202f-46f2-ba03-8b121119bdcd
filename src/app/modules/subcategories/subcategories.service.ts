import slugify from "slugify";
import ApiError from "../../middlewares/error";
import { ISubcategories } from "./subcategories.interface";
import Subcategories from "./subcategories.model";

class Service {
  async createSubcategory(subcategory: ISubcategories, role: string) {

    const isNameExist = await Subcategories.findOne({
      $or: [{ name_en: subcategory.name_en }, { name_bn: subcategory.name_bn }],
    });
    if (isNameExist) {
      throw new ApiError(500, "Category name already exist");
    }
    const totalCategory = await Subcategories.countDocuments({});
    const isPositionExist = await Subcategories.findOne({
      position: subcategory.position,
    });
    if (isPositionExist) {
      isPositionExist.position = totalCategory + 1;
      await isPositionExist.save();
    }
    // Generate slug from category name
    subcategory.slug = slugify(subcategory.name_en, {
      lower: true,
      strict: true,
    });
    const result = await Subcategories.create(subcategory);
    if (!result) {
      throw new ApiError(500, "Failed to create category");
    }
    return result;
  }

  async updateSubcategories(
    data: Partial<ISubcategories>, // accept partial updates
    id: string
  ) {
    const subcategory = await Subcategories.findById(id);
    if (!subcategory) {
      throw new ApiError(400, "Subcategory not exist");
    }
    // Generate slug from category name
    if (data.name_en && data.name_en) {
      data.slug = slugify(data.name_en, {
        lower: true,
        strict: true,
      });
    }

    const updatedFields: any = { ...data }; // dynamic fields

    // if position is being updated
    if (data.position !== undefined && data.position !== subcategory.position) {
      // Swap the position
      await Subcategories.findOneAndUpdate(
        { position: data.position, _id: { $ne: id } },
        { $set: { position: subcategory.position } }
      );
    }

    const updatedSubcategory = await Subcategories.findByIdAndUpdate(
      id,
      { $set: updatedFields },
      { new: true }
    );

    return updatedSubcategory;
  }

  async getAllSubcategories() {
    const subcategories = await Subcategories.find({})
      .sort({ position: 1 })
      .populate("category");
    return subcategories;
  }

  async getSubcategoriesByCategoryId(id: string) {
    const subcategories = await Subcategories.find({ category: id })
      .sort({ position: 1 })
      .populate("category");
    return subcategories;
  }
  async getSubcategoryById(id: string) {
    const subcategory = await Subcategories.findById(id).populate("category");
    if (!subcategory) {
      throw new ApiError(404, "Subcategory not found!")
    }
    return subcategory;
  }
}

export const SubcategoryService = new Service();
