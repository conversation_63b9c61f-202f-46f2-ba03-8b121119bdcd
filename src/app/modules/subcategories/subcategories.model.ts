import { model, Schema } from "mongoose";
import { ISubcategories, SubcategoryModel } from "./subcategories.interface";

const subcategoriesSchema = new Schema<ISubcategories>(
  {
    name_en: {
      type: String,
      required: true,
    },
    name_bn: {
      type: String,
      required: true,
    },
    description_en: {
      type: String,
      required: true,
    },
    description_bn: {
      type: String,
      required: true,
    },
    image_url: {
      type: String,
      required: false
    },
    position: {
      type: Number,
      required: false,
      default: 0
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      index: true,
      lowercase: true,
      trim: true,
    },
    category: {
      type: Schema.Types.ObjectId,
      ref: "categories",
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const Subcategories = model<ISubcategories, SubcategoryModel>(
  "subcategories",
  subcategoriesSchema
);

export default Subcategories;
