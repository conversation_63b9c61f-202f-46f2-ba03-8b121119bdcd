import { Document, Model } from "mongoose";
import { ICategory } from "../categories/categories.interface";

export type ISubcategories = {
  name_en: string;
  name_bn: string;
  description_en: string;
  description_bn: string;
  position: number;
  image_url?: string;
  slug: string;
  category: ICategory["_id"]; // reference to the category ID

} & Document;

export type SubcategoryModel = Model<ISubcategories, Record<string, unknown>>;
