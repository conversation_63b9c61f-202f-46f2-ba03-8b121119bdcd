import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { SubcategoryService } from "./subcategories.service";
import slugify from "slugify";

class Subcontroller extends BaseController {
  createsSubcategory = this.catchAsync(async (req: Request, res: Response) => {
    const payload = req.body;
    // Generate slug from name_en
    if (payload?.name_en) {
      payload.slug = slugify(payload?.name_en, {
        lower: true,
        strict: true,
      });
    }
    const data = await SubcategoryService.createSubcategory(payload, req.role);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Sub-category created successfully",
      data: data,
    });
  });
  updateSubcategory = this.catchAsync(async (req: Request, res: Response) => {
    const payload = req.body;
    // Update slug from name_en
    if (payload?.name_en) {
      payload.slug = slugify(payload?.name_en, {
        lower: true,
        strict: true,
      });
    }
    const data = await SubcategoryService.updateSubcategories(
      payload,
      req.params.sub_category_id
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Sub-category updated successfully",
      data: data,
    });
  });
  getAllSubcategory = this.catchAsync(async (req: Request, res: Response) => {
    const data = await SubcategoryService.getAllSubcategories();
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Sub-category found successfully",
      data: data,
    });
  });
  getSubcategoriesByCategoryId = this.catchAsync(async (req: Request, res: Response) => {
    const data = await SubcategoryService.getSubcategoriesByCategoryId(req.params.category_id as string);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Subcategory by category id found successfully",
      data: data,
    });
  });
  getSubcategoryById = this.catchAsync(async (req: Request, res: Response) => {
    const data = await SubcategoryService.getSubcategoryById(req.params.id as string);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Subcategory found successfully",
      data: data,
    });
  });
}

export const SubcategoryController = new Subcontroller();
