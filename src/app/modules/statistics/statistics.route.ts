import express from "express";
import { Statistics<PERSON>ontroller } from "./statistics.controller";
import verifyToken from "../../middlewares/verifyToken";

const router = express.Router();

router.put("/add-visitor", StatisticsController.addAndUpdateVisitor);

router.get("/visitors", verifyToken, StatisticsController.getVisitorsSummary);

router.get("/users", verifyToken, StatisticsController.getUserUpdateSummary);
router.get("/orders", verifyToken, StatisticsController.getOrdersUpdateSummary);
router.get(
  "/products",
  verifyToken,
  StatisticsController.getProductsUpdateSummary
);
router.get("/server-status", StatisticsController.getServerHealth);

export const StatisticsRoutes = router;
