import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { StatisticsService } from "./statistics.service";

class Controller extends BaseController {
  addAndUpdateVisitor = this.catchAsync(async (req: Request, res: Response) => {
    const data = await StatisticsService.addAndUpdateVisitor();
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Visitor added successfully",
      data: data,
    });
  });
  getVisitorsSummary = this.catchAsync(async (req: Request, res: Response) => {
    const data = await StatisticsService.getVisitorsSummary(
      req?.role,
      req.params.day || ""
    );
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Visitors statistics found successfully",
      data: data,
    });
  });
  getUserUpdateSummary = this.catchAsync(
    async (req: Request, res: Response) => {
      const data = await StatisticsService.getUserUpdateSummary(req?.role);
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Users statistics found successfully",
        data: data,
      });
    }
  );
  getOrdersUpdateSummary = this.catchAsync(
    async (req: Request, res: Response) => {
      const data = await StatisticsService.getOrdersUpdateSummary(req?.role);
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Orders statistics found successfully",
        data: data,
      });
    }
  );
  getProductsUpdateSummary = this.catchAsync(
    async (req: Request, res: Response) => {
      const data = await StatisticsService.getProductsUpdateSummary(req?.role);
      this.sendResponse(res, {
        statusCode: 200,
        success: true,
        message: "Products statistics found successfully",
        data: data,
      });
    }
  );
  getServerHealth = this.catchAsync(async (req: Request, res: Response) => {
    const data = { status: "healthy", message: "Server is working!" };
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Server status found successfully!",
      data: data,
    });
  });
}

export const StatisticsController = new Controller();
