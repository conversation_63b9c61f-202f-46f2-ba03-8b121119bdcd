import Orders from "../orders/order.model";
import Products from "../products/products.model";
import User from "../users/user.model";
import VisitorStatistics from "./statistics.model";

class Service {
  async addAndUpdateVisitor() {
    // Check if there are any existing statistics for today
    const stats = await VisitorStatistics.findOne();

    if (!stats) {
      // If no statistics exist, create a new record
      const newVisitor = await VisitorStatistics.create({
        total_visitors: 1,
        today_visitors: 1,
      });

      if (!newVisitor) {
        throw new Error("Failed to add visitor");
      }
      return newVisitor;
    }

    // If statistics exist, update the visitor data
    stats.total_visitors += 1;

    stats.today_visitors += 1;

    // Save the updated statistics
    const updatedVisitor = await stats.save();

    if (!updatedVisitor) {
      throw new Error("Failed to update visitor");
    }

    return updatedVisitor;
  }

  async resetYesterdayVisitors() {
    const totalVisitor = await VisitorStatistics.find();
    await VisitorStatistics.findOneAndUpdate(
      { _id: totalVisitor?.[0]?._id },
      { today_visitors: 0 },
      { new: true }
    );
  }
  async getVisitorsSummary(role: string, days: string) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new Error("You don't have access to view statistics");
    }

    const totalVisitor = await VisitorStatistics.find();

    return {
      totalVisitor: totalVisitor?.[0]?.total_visitors || 0,
      todayVisitors: totalVisitor?.[0]?.today_visitors || 0,
    };
  }

  async getUserUpdateSummary(role: string) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new Error("You don't have access to view statistics");
    }

    const [totalCustomer, totalSeller, totalSellerRequest] = await Promise.all([
      User.countDocuments({ role: "USER" }),
      User.countDocuments({ role: "SELLER" }),
      User.countDocuments({ "seller_request_info.status": "PENDING" }),
    ]);

    return {
      totalCustomer: totalCustomer || 0,
      totalSeller: totalSeller || 0,
      totalSellerRequest: totalSellerRequest || 0,
    };
  }

  async getOrdersUpdateSummary(role: string) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new Error("You don't have access to view statistics");
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set time to the start of the day

    // Count today's orders with the status "ORDERED"
    const newOrders = await Orders.countDocuments({
      status: "ORDERED",
      order_date: { $gte: today }, // Filter by today's date
    });

    // Count in-progress orders with statuses "PACKED" or "IN_TRANSIT"
    const inProgressOrders = await Orders.countDocuments({
      status: { $in: ["TRANSFER_TO_COURIER", "IN_TRANSIT"] },
    });

    // Count completed orders with the status "DELIVERED"
    const completedOrders = await Orders.countDocuments({
      status: "DELIVERED",
    });

    return {
      orders: {
        new: newOrders || 0,
        in_progress: inProgressOrders || 0,
        completed: completedOrders || 0,
      },
    };
  }

  async getProductsUpdateSummary(role: string) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new Error("You don't have access to view statistics");
    }

    // Count in-stock products (with at least one color having available_quantity > 0)
    const inStockProducts = await Products.countDocuments({
      "colors.available_quantity": { $gt: 0 },
    });

    // Count out-of-stock products (where all colors have available_quantity <= 0)
    const outOfStockProducts = await Products.countDocuments({
      "colors.available_quantity": { $lte: 0 },
    });

    const countProducts = await Products.countDocuments();

    // Get the last 30 days sales summary
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const salesSummary = await Orders.aggregate([
      {
        $match: {
          order_date: { $gte: thirtyDaysAgo },
          status: "DELIVERED", // Consider only delivered orders
        },
      },
      {
        $unwind: "$products",
      },
      {
        $group: {
          _id: "$products.product", // Group by product ID
          total_quantity: { $sum: "$products.quantity" }, // Sum the quantity
        },
      },
      {
        $group: {
          _id: null,
          total_quantity: { $sum: "$total_quantity" }, // Sum all quantities
          unique_product_count: { $sum: 1 }, // Count unique products
        },
      },
      {
        $project: {
          _id: 0,
          total_quantity: 1,
          unique_product_count: 1,
        },
      },
    ]);

    const { total_quantity = 0, unique_product_count = 0 } =
      salesSummary.length > 0 ? salesSummary[0] : {};

    return {
      products: {
        last_30days_sales: {
          total_quantity,
          unique_products: unique_product_count,
        },
        in_stock: inStockProducts || 0,
        stock_out: outOfStockProducts || 0,
        total_products: countProducts,
      },
    };
  }
}

export const StatisticsService = new Service();
