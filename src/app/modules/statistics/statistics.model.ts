import { Schema, model } from "mongoose";
import { IVisitor, VisitorModel } from "./statistics.interface";

const visitorSchema = new Schema<IVisitor>(
  {
    total_visitors: {
      type: Number,
      default: 0,
    },
    today_visitors: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const VisitorStatistics = model<IVisitor, VisitorModel>(
  "visitor-statistic",
  visitorSchema
);

export default VisitorStatistics;
