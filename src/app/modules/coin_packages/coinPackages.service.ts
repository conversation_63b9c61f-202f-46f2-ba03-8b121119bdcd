import ApiError from "../../middlewares/error";
import User from "../users/user.model";
import CoinPackages from "./coinPackages.model";

class Service {
  async createCoinPackage(
    role: string,
    data: { usable_coins: number; discount: number }
  ) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new ApiError(401, "You have no access to create coin package");
    }

    const result = await CoinPackages.create(data);
    if (!result) {
      throw new ApiError(500, "Failed to create coin package");
    }
    return result;
  }
  // Delete a coin package by marking it as isDeleted
  async deleteCoinPackage(role: string, id: string) {
    if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new ApiError(401, "You have no access to delete coin package");
    }

    const result = await CoinPackages.findByIdAndUpdate(
      id,
      { isDeleted: true },
      { new: true }
    );

    if (!result) {
      throw new ApiError(404, "Coin package not found");
    }

    return result;
  }

  // Get all coin packages based on role
  async getAllCoinPackages(role: string) {
    let filter = {};
    if (role === "USER") {
      filter = { isDeleted: false };
    } else if (role !== "ADMIN" && role !== "MODERATOR") {
      throw new ApiError(401, "You have no access to view coin packages");
    }

    const result = await CoinPackages.find(filter)
      .sort({ usable_coins: 1 })
      .exec();
    if (!result || result.length === 0) {
      throw new ApiError(404, "No coin packages found");
    }

    return result;
  }

  async claimCoinsPackage(coinPackageId: string, customerId: string) {
    const coinPackage = await CoinPackages.findById(coinPackageId).exec();
    if (!coinPackage || coinPackage.isDeleted) {
      throw new ApiError(404, "Coin package not found or has been deleted");
    }

    // Fetch the user
    const customer = await User.findById(customerId).select({
      available_coins: 1,
      claimed_coins: 1,
    });

    if (!customer) {
      throw new ApiError(404, "User not found");
    }

    // Check if user has enough coins
    if ((customer.available_coins ?? 0) < coinPackage.usable_coins) {
      throw new ApiError(403, "Not enough coins available!");
    }

    // Deduct the coins and update the history
    const updatedUser = await User.findByIdAndUpdate(
      customerId,
      {
        $inc: { available_coins: -coinPackage.usable_coins },
        $push: {
          claimed_coins: {
            coins: coinPackage.usable_coins,
            discount: coinPackage.discount,
            reference: {
              name: "ORDER",
              id: coinPackageId,
            },
          },
        },
      },
      { new: true }
    ).exec();

    if (!updatedUser) {
      throw new ApiError(500, "Failed to update user data");
    }

    return updatedUser;
  }
}

export const CoinPackageService = new Service();
