import { model, Schema } from "mongoose";
import { CoinPackageModel, ICoinPackage } from "./coinPackages.interface";

const coinPackagesSchema = new Schema<ICoinPackage>(
  {
    usable_coins: {
      type: Number,
      required: true,
    },
    discount: {
      type: Number,
      required: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    total_used: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  }
);

const CoinPackages = model<ICoinPackage, CoinPackageModel>(
  "coin_packages",
  coinPackagesSchema
);

export default CoinPackages;
