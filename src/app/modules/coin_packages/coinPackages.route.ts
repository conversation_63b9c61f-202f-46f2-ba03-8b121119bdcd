import express from "express";
import verifyToken from "../../middlewares/verifyToken";
import { CoinPackageController } from "./coinPackages.controller";

const router = express.Router();

router.post("/create", verifyToken, CoinPackageController.createCoinPackage);

router.patch(
  "/delete/:coin_package_id",
  verifyToken,
  CoinPackageController.deleteCoinPackage
);

router.get("/packages", verifyToken, CoinPackageController.getAllCoinPackages);

router.patch(
  "/claim/:coin_package_id/:customer_id",
  verifyToken,
  CoinPackageController.claimCoinsPackage
);

export const CoinPackageRoutes = router;
