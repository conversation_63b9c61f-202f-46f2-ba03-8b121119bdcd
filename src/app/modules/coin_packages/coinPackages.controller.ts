import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";
import { CoinPackageService } from "./coinPackages.service";

class Controller extends BaseController {
  // Create a coin package
  createCoinPackage = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CoinPackageService.createCoinPackage(req.role, req.body);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Coin package created successfully",
      data: data,
    });
  });

  // Delete a coin package
  deleteCoinPackage = this.catchAsync(async (req: Request, res: Response) => {
    const { coin_package_id } = req.params;
    await CoinPackageService.deleteCoinPackage(req.role, coin_package_id);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Coin package deleted successfully",
    });
  });

  // Get all coin packages
  getAllCoinPackages = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CoinPackageService.getAllCoinPackages(req.role);
    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Coin packages retrieved successfully",
      data: data,
    });
  });

  // Validate a coin package and customer coins
  claimCoinsPackage = this.catchAsync(async (req: Request, res: Response) => {
    const data = await CoinPackageService.claimCoinsPackage(
      req.params.coin_package_id,
      req.params.customer_id
    );

    this.sendResponse(res, {
      statusCode: 200,
      success: true,
      message: "Coins claimed successfully!",
      data: data,
    });
  });
}

export const CoinPackageController = new Controller();
