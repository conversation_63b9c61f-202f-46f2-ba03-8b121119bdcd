/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "axios";
import { get, set, unset } from "node-global-storage";
import { PaymentUtils } from "./payment.utils";
import { v4 as uuid } from "uuid";
import User from "../users/user.model";
import ApiError from "../../middlewares/error";
import Products from "../products/products.model";
import Coupons from "../coupons/coupons.model";

class Service {
  async createPayment(payload: any) {
    const isExistData = get(payload?.user_id);
    if (isExistData) {
      unset(payload?.user_id);
    }

    let total_amount;
    let callbackURL;
    // if (payload.event === "PRODUCT_PURCHASE") {
    //   // check is user valid
    //   const findUser = await User.findById({ _id: payload.user_id });
    //   if (!findUser?.id) {
    //     throw new ApiError(404, "Invalid user ID");
    //   }
    //   // Validate products
    //   const productIds = payload?.products?.map(
    //     (product: any) => product?.product
    //   );
    //   const products = await Products.find({ _id: { $in: productIds } });
    //   payload.products.forEach((productPayload: any) => {
    //     const product = products?.find(
    //       (p :any) => p._id.toString() === productPayload.product
    //     );
    //     if (!product) {
    //       throw new ApiError(
    //         400,
    //         `Product with ID ${productPayload.product} not found`
    //       );
    //     }

    //     if (product.price !== productPayload.price) {
    //       throw new ApiError(
    //         400,
    //         `Invalid price for product with ID ${productPayload.product}`
    //       );
    //     }
    //     const variantExit = product.variant.some(
    //       (colorObj) => colorObj.name === productPayload.name
    //     );
    //     if (!variantExit) {
    //       throw new ApiError(
    //         400,
    //         `Invalid color for product with ID ${productPayload.product}`
    //       );
    //     }
    //   });

    //   if (products.length !== payload.products.length) {
    //     throw new ApiError(400, "One or more products are invalid");
    //   }

    //   if (payload.coupon) {
    //     // Validate coupon
    //     const coupon = await Coupons.findById({ _id: payload.coupon });
    //     if (!coupon) {
    //       throw new ApiError(400, "Invalid coupon code");
    //     }
    //     if (coupon.status !== "ACTIVE") {
    //       throw new ApiError(400, "Coupon expired");
    //     }
    //     if (coupon.discount !== payload.applied_coupon_discount) {
    //       throw new ApiError(400, "Coupon amount is not valid");
    //     }
    //   }

    //   if (payload.claimed_coins_id) {
    //     // Validate claimed_coins_id
    //     const claimedCoin = findUser.claimed_coins?.find(
    //       (coin) => coin.reference.id == payload.claimed_coins_id
    //     );

    //     if (!claimedCoin) {
    //       throw new ApiError(400, "Invalid claimed coins ID");
    //     }

    //     // Ensure claimed coins match the payload discount
    //     if (claimedCoin.discount !== payload.applied_coins_discount) {
    //       throw new ApiError(400, "Invalid claimed coins discount amount");
    //     }
    //   }

    //   total_amount = payload?.payable_amount;
    //   callbackURL = `${process.env.SERVER_URL}/api/v1/order/create-with-payment?user_id=${payload.user_id}`;
    // } else {
    //   throw new ApiError(400, "Invalid event type");
    // }

    set(payload.user_id, payload);

    if (payload.payable_amount === 0) {
      return;
    }

    try {
      const { data } = await axios.post(
        process.env.bkash_create_payment_url as string,
        {
          mode: "0011",
          payerReference: " ",
          callbackURL: callbackURL,
          amount: total_amount,
          currency: "BDT",
          intent: "sale",
          merchantInvoiceNumber: "Inv" + uuid().substring(0, 5),
        },
        {
          headers: await PaymentUtils.bkash_headers(),
        }
      );

      return data;
    } catch (error: any) {
      if (error.response) {
        console.error("From Create Payment", error.response);
      } else {
        console.error("From Create Payment Error Message", error.message);
      }
      throw new ApiError(400, "Payment request failed");
    }
  }
}

export const PaymentService = new Service();
