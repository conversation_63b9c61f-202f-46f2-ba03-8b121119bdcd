/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "axios";
import { NextFunction, Request, Response } from "express";
import { get, set, unset } from "node-global-storage";
import ApiError from "../../middlewares/error";

class Middleware {
  bkash_auth = async (req: Request, res: Response, next: NextFunction) => {
    const idToken = get("id_token");
    if (idToken) {
      unset("id_token");
    }
    try {
      const { data } = await axios.post(
        process.env.bkash_grant_token_url as string,
        {
          app_key: process.env.bkash_app_key,
          app_secret: process.env.bkash_app_secret,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            username: process.env.bkash_username,
            password: process.env.bkash_password,
          },
        }
      );
      if (data.id_token) {
        set("id_token", data.id_token, { protected: true });
        next();
      } else {
        throw new ApiError(400, "Invalid Request");
      }
    } catch (error: any) {
      console.log("error", error);
      throw new ApiError(400, error.message);
    }
  };
}
export const BkashMiddleware = new Middleware();
