import { Request, Response } from "express";
import BaseController from "../../../shared/baseController";

import { PaymentService } from "./payment.service";

class Controller extends BaseController {
  createPayment = this.catchAsync(async (req: Request, res: Response) => {
    const data = await PaymentService.createPayment(req.body);
    res.status(200).json({ bkashURL: data.bkashURL });
  });
}

export const PaymentController = new Controller();
