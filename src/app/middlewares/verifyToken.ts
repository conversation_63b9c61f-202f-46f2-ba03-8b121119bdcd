import { NextFunction, Request, Response } from "express";
import { JwtPayload, Secret } from "jsonwebtoken";
import JwtHelpers from "../helpers/jwtHelpers";
import ApiError from "./error";
import { IRoles } from "../modules/users/user.constant";

// Define a custom type for extending the Request object
type CustomRequest = Request & {
  id?: string;
  phone_number?: string;
  role?: string;
};

// Define a custom type for extending the JWT Payload object
type CustomJWTPayload = JwtPayload & {
  id?: string;
  phone_number?: string;
  role?: string;
};

const verifyToken =
  (allowedRoles?: IRoles[]) =>
  (req: CustomRequest, res: Response, next: NextFunction) => {
    const raw_token = req.headers.authorization;

    if (!raw_token) {
      throw new ApiError(400, "Token not found");
    }
    let token = raw_token; // Default to the raw token if no "Bearer" prefix
    if (raw_token.startsWith("Bearer ")) {
      token = raw_token.split(" ")[1]; // Extract the token part after "Bearer "
    }
    const isVerified: CustomJWTPayload | null = JwtHelpers.verifyToken(
      token,
      process.env.JWT_TOKEN as Secret
    );

    if (
      !isVerified ||
      !isVerified.id ||
      !isVerified.phone_number ||
      !isVerified.role
    ) {
      throw new ApiError(400, "Invalid or incomplete token payload");
    }

    // If allowedRoles is provided and non-empty, check if role is permitted
    if (Array.isArray(allowedRoles) && allowedRoles.length > 0) {
      if (!allowedRoles.includes(isVerified.role as IRoles)) {
        throw new ApiError(403, "Forbidden: Access denied");
      }
    }

    // Attach user info to request
    req.id = isVerified.id;
    req.phone_number = isVerified.phone_number;
    req.role = isVerified.role;

    next();
  };

export default verifyToken;
