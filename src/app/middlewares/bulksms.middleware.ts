/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "axios";
import { Request, Response } from "express";
import ApiError from "./error";
import { OTPService } from "../modules/otp/otp.service";
const otpGenerator = require("otp-generator");

class Middleware {
  send_otp = async (
    number: string
  ): Promise<{ success: boolean; message: string; otp: number }> => {
    try {
      const otpCode = otpGenerator.generate(5, {
        upperCaseAlphabets: false,
        specialChars: false,
        lowerCaseAlphabets: false,
      });
      const otpAddedRes: any = await OTPService.addOTPRecord({
        number,
        otp: otpCode,
      });

      if (otpAddedRes?.has_otp) {
        return {
          success: true,
          message: "OTP already sent",
          otp: otpCode,
        };
      }
      const payload = {
        api_key: process.env.bulk_sms_api_key,
        senderid: process.env.bulk_sms_sender_id,
        number: number,
        message: `Your Gadget Glitz verification code is ${otpCode}`,
      };
      const { data } = await axios.post(
        `http://bulksmsbd.net/api/smsapi`,
        payload
      );
      if (data.response_code === 202) {
        return {
          success: true,
          message: "OTP sent successfully",
          otp: otpCode,
        };
      } else {
        console.log(`BULKSMS ERROR:`, data);
        return {
          success: false,
          message: "OTP sending failed!",
          otp: 0,
        };
      }
    } catch (error: any) {
      console.log("SENT OTP ERROR", error);
      throw new ApiError(400, error.message);
    }
  };
  send_sms = async (number: string, message: string) => {
    try {
      const payload = {
        api_key: process.env.bulk_sms_api_key,
        senderid: process.env.bulk_sms_sender_id,
        number: number,
        message: message,
      };
      const { data } = await axios.post(
        `http://bulksmsbd.net/api/smsapi`,
        payload
      );
      if (data.response_code === 202) {
        return {
          success: true,
          message: "SMS sent successfully",
        };
      } else {
        console.log(`SENT SMS ERROR:`, data);
        return {
          success: false,
          message: "SMS sent failed!",
        };
      }
    } catch (error: any) {
      console.log("SENT SMS ERROR", error);
      new ApiError(400, error.message);
    }
  };
}

export const BulkSMSMiddleware = new Middleware();
