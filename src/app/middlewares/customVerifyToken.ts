import { NextFunction, Request, Response } from "express";
import { JwtPayload, Secret } from "jsonwebtoken";
import JwtHelpers from "../helpers/jwtHelpers";
import ApiError from "./error";

// Define a custom type for extending the Request object
type CustomRequest = Request & {
  id?: string;
  email?: string;
  role?: string;
};

// Define a custom type for extending the JWT Payload object
type CustomJWTPayload = JwtPayload & {
  id?: string;
  email?: string;
  role?: string;
};

const customVerifyToken = (
  req: CustomRequest,
  res: Response,
  next: NextFunction
) => {
  const token = req.headers.authorization;
  if (token) {
    const isVerified: CustomJWTPayload | null = JwtHelpers.verifyToken(
      token,
      process.env.JWT_TOKEN as Secret
    );
    if (!isVerified) {
      throw new ApiError(400, "Invalid token");
    } else {
      // Assign custom properties to the req object
      req.id = isVerified?.id as string;
      req.email = isVerified?.email as string;
      req.role = isVerified?.role as string;

      next();
    }
  } else {
    next();
  }
};

export default customVerifyToken;
