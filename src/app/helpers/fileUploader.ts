import env from "../../config";
import axios from "axios";

export interface UploadResult {
  url: string;
  name: string;
  key: string;
}

interface ImageStorage {
  uploadImage(
    buffer: Buffer,
    filename: string,
    contentType: string
  ): Promise<UploadResult>;
  deleteImage(key: string): Promise<void>;
  listImages(): Promise<UploadResult[]>;
}

export class UploadThingImageStorage implements ImageStorage {
  private readonly apiKey: string;
  private readonly baseUrl = "https://api.uploadthing.com";

  constructor() {
    this.apiKey = env.uploadthing_token as string;
  }

  async uploadImage(
    buffer: Buffer,
    filename: string,
    contentType: string
  ): Promise<UploadResult> {
    console.log(contentType);
    try {
      // Step 1: Request presigned URL
      const presignedResponse = await axios.post(
        `${this.baseUrl}/v6/uploadFiles`,
        {
          files: [
            {
              name: filename,
              type: contentType,
              size: buffer.length,
            },
          ],
        },
        {
          headers: {
            "X-Uploadthing-Api-Key": this.apiKey,
            "Content-Type": "application/json",
          },
        }
      );

      const { data } = presignedResponse.data;
      if (!data || !data[0]) {
        throw new Error("Failed to get presigned URL");
      }

      const fileData = data[0];
      const { url: presignedUrl, fields, key } = fileData;

      // Step 2: Upload to presigned URL
      const formData = new FormData();

      // Add all the fields from the presigned response
      Object.entries(fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });

      // Add the file
      const blob = new Blob([buffer], { type: contentType });
      formData.append("file", blob, filename);

      await axios.post(presignedUrl, formData, {
        headers: {
          // Don't set Content-Type header, let axios handle it for FormData
        },
      });

      // Step 3: Poll for completion (UploadThing processes files asynchronously)
      const fileUrl = `https://utfs.io/f/${key}`;

      return {
        url: fileUrl,
        name: filename,
        key: key,
      };
    } catch (err) {
      console.error("Upload error:", err);
      throw err;
    }
  }

  async deleteImage(key: string): Promise<void> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/v6/deleteFiles`,
        {
          fileKeys: [key],
        },
        {
          headers: {
            "X-Uploadthing-Api-Key": this.apiKey,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.data.success) {
        throw new Error(`Delete failed for key: ${key}`);
      }
    } catch (err) {
      console.error("Delete error:", err);
      throw err;
    }
  }

  async listImages(): Promise<UploadResult[]> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/v6/listFiles`,
        {
          limit: 1000,
          offset: 0,
        },
        {
          headers: {
            "X-Uploadthing-Api-Key": this.apiKey,
            "Content-Type": "application/json",
          },
        }
      );

      const files = response.data.files || [];
      return files.map((file: any) => ({
        url: `https://utfs.io/f/${file.key}`,
        name: file.name,
        key: file.key,
      }));
    } catch (err) {
      console.error("List error:", err);
      throw err;
    }
  }

  // Additional utility methods
  async renameImage(key: string, newName: string): Promise<void> {
    try {
      await axios.post(
        `${this.baseUrl}/v6/renameFiles`,
        {
          updates: [
            {
              fileKey: key,
              newName: newName,
            },
          ],
        },
        {
          headers: {
            "X-Uploadthing-Api-Key": this.apiKey,
            "Content-Type": "application/json",
          },
        }
      );
    } catch (err) {
      console.error("Rename error:", err);
      throw err;
    }
  }

  async getFileInfo(key: string): Promise<any> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/v6/getFileUrls`,
        {
          fileKeys: [key],
        },
        {
          headers: {
            "X-Uploadthing-Api-Key": this.apiKey,
            "Content-Type": "application/json",
          },
        }
      );

      return response.data.data[0];
    } catch (err) {
      console.error("Get file info error:", err);
      throw err;
    }
  }
}
