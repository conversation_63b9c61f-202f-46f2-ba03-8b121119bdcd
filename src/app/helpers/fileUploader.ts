export interface UploadResult {
  url: string;
  name: string;
  key: string;
}

interface ImageStorage {
  uploadImage(
    buffer: Buffer,
    filename: string,
    contentType: string
  ): Promise<UploadResult>;
  deleteImage(key: string): Promise<void>;
  listImages(): Promise<UploadResult[]>;
}

import axios from "axios";

export class UploadThingImageStorage implements ImageStorage {
  private static readonly uploadUrl = "https://uploadthing.com/api/uploadFiles";
  private static readonly listUrl = "https://uploadthing.com/api/listFiles";
  private static readonly deleteUrl = "https://uploadthing.com/api/deleteFile";

  private static readonly apiKey: string = process.env
    .UPLOADTHING_TOKEN as string;

  constructor() {}

  static async uploadImage(
    buffer: Buffer,
    filename: string,
    contentType: string
  ): Promise<UploadResult> {
    const form = new FormData();
    const blob = new Blob([buffer], { type: contentType });
    form.append("files", blob, filename);

    const response = await axios.post(`${this.uploadUrl}`, form, {
      headers: {
        "X-Uploadthing-Api-Key": this.apiKey,
      },
    });

    const file = response.data[0];
    return {
      url: file.url,
      name: file.name,
      key: file.key,
    };
  }

  static async deleteImage(key: string): Promise<void> {
    await axios.post(
      this.deleteUrl,
      { key },
      {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
      }
    );
  }

  static async listImages(): Promise<UploadResult[]> {
    const response = await axios.get(this.listUrl, {
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
    });

    return response.data.map((file: any) => ({
      url: file.url,
      name: file.name,
      key: file.key,
    }));
  }
}
