/* eslint-disable @typescript-eslint/no-explicit-any */
import cookieParser from "cookie-parser";
import cors from "cors";
import dotenv from "dotenv";
import express, { NextFunction, Request, Response } from "express";
import globalErrorHandler from "./app/middlewares/globalErrorHandler";
import router from "./app/routes";
import body_parser from "body-parser";
dotenv.config();

const app = express();

const allowedOrigins = [
  "https://www.gadgetglitzbd.com",
  "https://www.gadgetglitzbd.com/",
  "https://gadgetglitzbd.com",
  "https://gadgetglitzbd.com/",
  "https://admin.gadgetglitzbd.com",
  "https://admin.gadgetglitzbd.com/",
  "http://localhost:3000",
  "http://localhost:3000/",
  "http://localhost:3001",
  "http://localhost:3001/",
];

app.use(
  cors({
    origin: function (origin, callback) {
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error("Not allowed by CORS"));
      }
    },
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS",
    credentials: true,
  })
);

app.use(body_parser.json());

app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));
app.use(cookieParser());
app.get("/", async (req, res) => {
  res.status(200).json({
    statusCode: 200,
    success: true,
    message: "Gadget Glitz server is running!",
    data: null,
  });
});

app.use("/api/v1", router);

app.use(globalErrorHandler.globalErrorHandler);

app.use((req: Request, res: Response, next: NextFunction) => {
  res.status(404).json({
    success: false,
    message: "Not Found",
    errorMessages: [
      {
        path: req.originalUrl,
        message: "API Not Found",
      },
    ],
  });
  next();
});

export default app;
