{"name": "gadget-glitz-server", "version": "1.0.0", "description": "", "main": "server.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "tsnd --respawn --rs --transpile-only ./src/server.ts", "lint:check": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "lint:fix": "eslint . --fix", "prettier:check": "prettier --ignore-path .es<PERSON><PERSON><PERSON> --write \"**/*.+(js|ts|json)\"", "prettier:fix": "prettier --write .", "lint-prettier": "npm run lint:check && npm run prettier:check", "build": "tsc", "lint-staged": "lint-staged", "start": "node ./dist/server.js"}, "lint-staged": {"src/**/*.ts": "npm run lint-prettier"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.8", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.2.1", "multer": "^2.0.1", "node-cron": "^3.0.3", "node-global-storage": "^2.0.0", "nodemailer": "^6.9.12", "otp-generator": "^4.0.1", "slugify": "^1.6.6", "uploadthing": "^7.7.3", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^2.0.0", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "checkly": "^6.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.0", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.2"}, "packageManager": "pnpm@9.13.0+sha512.beb9e2a803db336c10c9af682b58ad7181ca0fbd0d4119f2b33d5f2582e96d6c0d93c85b23869295b765170fbdaa92890c0da6ada457415039769edf3c959efe"}