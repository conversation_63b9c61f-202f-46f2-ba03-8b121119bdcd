### Create Category with Image Upload
POST http://localhost:5000/api/v1/category
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_en"

Electronics

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_bn"

ইলেকট্রনিক্স

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="position"

1

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="description_en"

Electronics

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="description_bn"

ইলেকট্রনিক্স

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="subcategories"

["Smartphones", "Laptops", "Tablets"]

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="image"; filename="category-image.jpg"
Content-Type: image/jpeg

< ./path/to/your/image.jpg

------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Alternative: Create Category without Image
POST http://localhost:5000/api/v1/category
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_en"

Fashion

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_bn"

ফ্যাশন

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="position"

2

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="subcategories"

["Men's Clothing", "Women's Clothing", "Accessories"]

------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Get All Categories
GET http://localhost:5000/api/v1/categories

### Get Category by ID
GET http://localhost:5000/api/v1/categories/CATEGORY_ID_HERE

### Update Category with Image
PATCH http://localhost:5000/api/v1/categories/CATEGORY_ID_HERE
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_en"

Updated Electronics

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_bn"

আপডেটেড ইলেকট্রনিক্স

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="position"

1

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="subcategories"

["Smartphones", "Laptops", "Tablets", "Smart Watches"]

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="image"; filename="updated-category-image.jpg"
Content-Type: image/jpeg

< ./path/to/your/updated-image.jpg

------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Update Category without Image
PATCH http://localhost:5000/api/v1/categories/CATEGORY_ID_HERE
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_en"

Updated Fashion

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name_bn"

আপডেটেড ফ্যাশন

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="position"

2

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="subcategories"

["Men's Clothing", "Women's Clothing", "Accessories", "Shoes"]

------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Example using curl commands (alternative to HTTP file)

# Create Category with Image
# curl -X POST http://localhost:5000/api/v1/categories \
#   -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
#   -F "name_en=Electronics" \
#   -F "name_bn=ইলেকট্রনিক্স" \
#   -F "position=1" \
#   -F "subcategories=[\"Smartphones\", \"Laptops\", \"Tablets\"]" \
#   -F "image=@/path/to/your/image.jpg"

# Update Category with Image
# curl -X PATCH http://localhost:5000/api/v1/categories/CATEGORY_ID_HERE \
#   -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
#   -F "name_en=Updated Electronics" \
#   -F "name_bn=আপডেটেড ইলেকট্রনিক্স" \
#   -F "position=1" \
#   -F "subcategories=[\"Smartphones\", \"Laptops\", \"Tablets\", \"Smart Watches\"]" \
#   -F "image=@/path/to/your/updated-image.jpg"

### Notes:
# 1. Replace YOUR_JWT_TOKEN_HERE with actual JWT token from login
# 2. Replace ./path/to/your/image.jpg with actual image file path
# 3. Replace CATEGORY_ID_HERE with actual category ID
# 4. Make sure UPLOADTHING_TOKEN is set in your .env file
# 5. The API expects multipart/form-data for file uploads
# 6. subcategories should be a JSON string array
# 7. position should be a number
# 8. Only ADMIN users can create/update categories (check UserRole.ADMIN)
