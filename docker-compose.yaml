# version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${PORT}:5007"
    
    # depends_on:
    #   - mongo
    #   - redis
    # environment:
    #   - NODE_ENV=production
    #   - MONGODB_URI=mongodb://mongo:${MONGO_PORT}/${MONGO_DB}
    #   - REDIS_HOST=redis
    #   - REDIS_PORT=${REDIS_PORT}
    
    env_file:
      - .env
    networks:
      - gadget-glitz-server-network

  # mongo:
  #   image: mongo:latest
  #   restart: always
  #   ports:
  #     - "${MONGO_PORT}:27017"
  #   environment:
  #     - MONGO_INITDB_DATABASE=${MONGO_DB}
  #   volumes:
  #     - mongo-data:/data/db 
  #   logging:
  #     driver: "none"
  #   networks:
  #     - gadget-glitz-server-network

  # redis:
  #   image: redis:latest
  #   ports:
  #     - "${REDIS_PORT}:6379"
  #   volumes:
  #     - redis-data:/data
  #   networks:
  #     - gadget-glitz-server-network

# volumes:
#   mongo-data:
#   redis-data:

networks:
  gadget-glitz-server-network:
    driver: bridge

