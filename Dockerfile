# Use official Node.js image
FROM node:alpine

# Set working directory
WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm install

# Copy the rest of the source code
COPY . .

# Compile TypeScript to JavaScript
RUN npm run build

# Expose your app port
EXPOSE 5007
# Set environment variables 

# Start the app (now dist/server.js will exist)
# CMD ["tail", "-f", "/dev/null"]
CMD [ "npm", "run", "start" ]
