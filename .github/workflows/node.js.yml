name: Build & Deploy Gadget glitz api server

on:
  push:
    branches: ["main"]

jobs:
  build:
    runs-on: self-hosted
    environment: PRODUCTION_DEPLOYMENT

    strategy:
      matrix:
        node-version: [23.9.0]

    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
      - name: Check runner user and groups
        run: |
          whoami
          groups
      - name: copy .env file
        run: |
          echo "Copying .env file from env secrets"
          echo "${{secrets.ENV_FILE}}" | sed 's/\\n/\n/g' > .env
      - name: Set up Docker Compose
        run: |
          echo "up docker compose"
          docker compose up -d --build
