DATABASE_URL=mongodb+srv://exprobd_user:<EMAIL>/gadget-glitz-dev?retryWrites=true&w=majority&appName=Cluster0

PORT = 5000
HASH_SECRET=8d85a42d457a66c5dabf41c7e0387dba
ENVIRONMENT=PRODUCTION
LOCAL_CLIENT_URL=http://localhost:3000
SERVER_URL=
PRODUCTION_CLIENT_URL=https://gadgetglitzbd.com
JWT_TOKEN=c1ac602f148d6332decbbdfab81e3f863fbc12a6a65f6819aeb3368442cf0ff16e84d6ecb026b8a6b1deb83198057131543e785a6c02954c6963d1490c8fc9bd
ACCESS_EXPIRES_IN=7d
JWT_REFRESH_TOKEN=9f23cdf3c3d7ac69acaa4f9bf939e503a3f00e1408616111eb85493170aee8966495f61a60da848b79a733b9a2d2ea1941884f728e9d8eee6a3be276c50a4226
REFRESH_EXPIRES_IN=30d



# development
# bkash related url stuff
# bkash_grant_token_url = https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/token/grant

# bkash_create_payment_url =  https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/create

# bkash_execute_payment_url = https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/execute

# bkash_refund_transaction_url = https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/payment/refund


# # bkash credential related stuff

# bkash_username = 'sandboxTokenizedUser02'
# bkash_password = 'sandboxTokenizedUser02@12345'
# bkash_app_key = '4f6o0cjiki2rfm34kfdadl1eqq'
# bkash_app_secret = '2is7hdktrekvrbljjh44ll3d9l1dtjo4pasmjvs5vl5qr3fug4b'


# droduction
# bkash related url stuff
bkash_grant_token_url = 

bkash_create_payment_url =  

bkash_execute_payment_url =

bkash_refund_transaction_url = 


# bkash credential related stuff

bkash_username =
bkash_password = 
bkash_app_key = 
bkash_app_secret = 


# bulk sms credential 

bulk_sms_api_key = 
bulk_sms_sender_id = 

# courier key and secret
courier_api_key = 
courier_secret_key=  
courier_mayons_api_key =   
courier_mayons_secret_key= 
